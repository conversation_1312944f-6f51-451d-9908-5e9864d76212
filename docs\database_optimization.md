# 数据库优化建议

## 索引优化建议

基于当前的查询模式，建议添加以下索引来提高查询性能：

### Device表索引

```sql
-- 设备ID查询索引（用于搜索和筛选）
CREATE INDEX idx_device_device_id ON device(device_id);

-- 产品密钥索引（用于筛选）
CREATE INDEX idx_device_product_key ON device(product_key);

-- 固件版本索引（用于筛选）
CREATE INDEX idx_device_firmware_version ON device(firmware_version);

-- 设备备注索引（用于搜索）
CREATE INDEX idx_device_remark ON device(device_remark);

-- OTA状态索引（用于筛选）
CREATE INDEX idx_device_ota_status ON device(last_ota_status);

-- 创建时间索引（用于日期筛选）
CREATE INDEX idx_device_created_at ON device(created_at);

-- 复合索引：产品密钥+固件版本（常用组合查询）
CREATE INDEX idx_device_product_firmware ON device(product_key, firmware_version);

-- 复合索引：设备类型+固件版本（批量OTA筛选）
CREATE INDEX idx_device_type_firmware ON device(device_type, firmware_version);
```

### OtaTask表索引

```sql
-- 设备ID索引（用于查询设备的OTA历史）
CREATE INDEX idx_ota_task_device_id ON ota_task(device_id);

-- 状态索引（用于状态筛选）
CREATE INDEX idx_ota_task_status ON ota_task(status);

-- 固件版本索引（用于固件筛选）
CREATE INDEX idx_ota_task_firmware_version ON ota_task(firmware_version);

-- 创建时间索引（用于日期筛选和排序）
CREATE INDEX idx_ota_task_created_at ON ota_task(created_at);

-- 更新时间索引（用于排序）
CREATE INDEX idx_ota_task_updated_at ON ota_task(updated_at);

-- 复合索引：状态+创建时间（常用组合查询）
CREATE INDEX idx_ota_task_status_created ON ota_task(status, created_at);
```

### Firmware表索引

```sql
-- 版本索引（用于版本查询）
CREATE INDEX idx_firmware_version ON firmware(version);

-- 设备类型索引（用于类型筛选）
CREATE INDEX idx_firmware_device_type ON firmware(device_type);

-- 上传时间索引（用于排序）
CREATE INDEX idx_firmware_upload_time ON firmware(upload_time);

-- CRC32索引（用于CRC筛选）
CREATE INDEX idx_firmware_crc32 ON firmware(crc32);

-- 复合索引：设备类型+上传时间（常用组合查询）
CREATE INDEX idx_firmware_type_upload ON firmware(device_type, upload_time);
```

## 查询优化建议

### 1. 分页查询优化

当前的分页查询可以通过以下方式优化：

```python
# 优化前：使用OFFSET可能导致性能问题
query.paginate(page=page, per_page=per_page)

# 优化后：使用游标分页（适用于大数据集）
# 基于ID或时间戳的游标分页
last_id = request.args.get('last_id', 0, type=int)
query = query.filter(Device.id > last_id).limit(per_page + 1)
```

### 2. 状态筛选优化

对于设备状态筛选，建议：

```python
# 优化前：在Python中筛选状态
all_devices = query.all()
# 然后在内存中筛选...

# 优化后：将状态信息存储在数据库中
# 添加设备状态表或在设备表中添加状态字段
```

### 3. 批量查询优化

```python
# 优化前：循环查询
for device in devices:
    firmware = Firmware.query.filter_by(version=device.firmware_version).first()

# 优化后：批量查询
firmware_versions = [d.firmware_version for d in devices]
firmwares = Firmware.query.filter(Firmware.version.in_(firmware_versions)).all()
firmware_dict = {f.version: f for f in firmwares}
```

### 4. 统计查询优化

```python
# 优化前：多次查询
total_tasks = OtaTask.query.count()
success_count = OtaTask.query.filter_by(status='成功').count()
failed_count = OtaTask.query.filter_by(status='失败').count()

# 优化后：单次聚合查询
from sqlalchemy import func, case
stats = db.session.query(
    func.count(OtaTask.id).label('total'),
    func.sum(case([(OtaTask.status == '成功', 1)], else_=0)).label('success'),
    func.sum(case([(OtaTask.status == '失败', 1)], else_=0)).label('failed'),
    func.sum(case([(OtaTask.status == '进行中', 1)], else_=0)).label('in_progress'),
    func.sum(case([(OtaTask.status == '等待中', 1)], else_=0)).label('waiting')
).first()
```

## 缓存策略优化

### 1. 分层缓存

```python
# L1缓存：应用内存缓存（快速访问）
# L2缓存：Redis缓存（持久化，支持集群）
# L3缓存：数据库查询结果缓存
```

### 2. 缓存键设计

```python
# 使用层次化的缓存键
CACHE_KEYS = {
    'device_list': 'device:list:{page}:{per_page}:{filters_hash}',
    'device_stats': 'device:stats',
    'ota_task_list': 'ota:task:list:{page}:{per_page}:{filters_hash}',
    'ota_task_stats': 'ota:task:stats',
    'firmware_list': 'firmware:list'
}
```

### 3. 缓存失效策略

```python
# 基于事件的缓存失效
class CacheInvalidator:
    @staticmethod
    def on_device_status_change(device_id):
        cache.delete_pattern('device:*')
    
    @staticmethod
    def on_ota_task_update(task_id):
        cache.delete_pattern('ota:*')
        cache.delete_pattern('device:*')  # 因为设备状态可能变化
    
    @staticmethod
    def on_firmware_upload():
        cache.delete('firmware:list')
```

## 数据库连接池优化

```python
# 在config.py中优化连接池配置
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 20,           # 基础连接池大小
    'pool_timeout': 30,        # 连接超时时间
    'pool_recycle': 3600,      # 连接回收时间
    'max_overflow': 10,        # 允许的溢出连接数
    'pool_pre_ping': True,     # 连接前检查连接有效性
}
```

## 监控和分析

### 1. 查询性能监控

```python
# 添加查询时间监控
import time
from functools import wraps

def monitor_query_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        query_time = end_time - start_time
        if query_time > 1.0:  # 超过1秒的查询记录警告
            logger.warning(f"慢查询检测: {func.__name__} 耗时 {query_time:.2f}s")
        
        return result
    return wrapper
```

### 2. 缓存命中率监控

```python
# 在缓存服务中添加统计
class CacheService:
    def __init__(self):
        self.hit_count = 0
        self.miss_count = 0
    
    def get_hit_rate(self):
        total = self.hit_count + self.miss_count
        return (self.hit_count / total * 100) if total > 0 else 0
```

## 实施建议

1. **分阶段实施**：先实施缓存优化，再进行数据库索引优化
2. **监控效果**：实施后监控查询性能和缓存命中率
3. **A/B测试**：对比优化前后的性能差异
4. **定期维护**：定期分析慢查询日志，持续优化

## 预期效果

- **页面加载速度**：提升50-80%
- **数据库负载**：减少60-70%
- **并发处理能力**：提升2-3倍
- **用户体验**：显著改善响应速度
