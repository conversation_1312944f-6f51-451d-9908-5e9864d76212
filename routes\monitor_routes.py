from flask import Blueprint, render_template, jsonify
from flask_login import login_required
import psutil
import platform
import datetime
import os
from functools import lru_cache, wraps
import time
import threading
from utils.logger import setup_logging
import socket

monitor_bp = Blueprint('monitor', __name__)

# 系统信息缓存
system_info_cache = {}
cache_lock = threading.Lock()

# 创建更新事件
update_event = threading.Event()
# 创建停止事件
stop_event = threading.Event()

logger = setup_logging()

# 后台更新线程
def background_update_thread():
    """持续运行的后台线程，负责更新系统信息"""
    while not stop_event.is_set():
        # 等待更新事件被触发
        if update_event.wait(timeout=30.0):
            # 清除事件，准备下一次触发
            update_event.clear()

            # 执行所有更新函数
            update_system_info()
            update_cpu_info()
            update_memory_info()
            update_disk_info()
            update_network_info()
            update_processes()

# 启动后台线程
background_thread = threading.Thread(target=background_update_thread)
background_thread.daemon = True  # 设置为守护线程
background_thread.start()

def update_system_info():
    """在后台更新系统信息"""
    try:
        info = {
            'os': platform.system(),
            'os_version': platform.version(),
            'hostname': platform.node(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'boot_time': datetime.datetime.fromtimestamp(psutil.boot_time()).strftime('%Y-%m-%d %H:%M:%S'),
            'architecture': platform.machine()
        }
        with cache_lock:
            system_info_cache['system'] = info
    except Exception as e:
        with cache_lock:
            system_info_cache['system'] = {'error': str(e)}

def update_cpu_info():
    """在后台更新CPU信息"""
    try:
        info = {
            'percent': psutil.cpu_percent(interval=None),
            'freq': {
                'current': psutil.cpu_freq().current,
                'min': psutil.cpu_freq().min,
                'max': psutil.cpu_freq().max
            },
            'count': {
                'physical': psutil.cpu_count(logical=False),
                'logical': psutil.cpu_count(logical=True)
            }
        }
        with cache_lock:
            system_info_cache['cpu'] = info
    except Exception as e:
        with cache_lock:
            system_info_cache['cpu'] = {'error': str(e)}

def update_memory_info():
    """在后台更新内存信息"""
    try:
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        info = {
            'memory': {
                'total': round(memory.total / (1024**3), 2),
                'available': round(memory.available / (1024**3), 2),
                'used': round(memory.used / (1024**3), 2),
                'percent': memory.percent
            },
            'swap': {
                'total': round(swap.total / (1024**3), 2),
                'used': round(swap.used / (1024**3), 2),
                'free': round(swap.free / (1024**3), 2),
                'percent': swap.percent
            }
        }
        with cache_lock:
            system_info_cache['memory'] = info
    except Exception as e:
        with cache_lock:
            system_info_cache['memory'] = {'error': str(e)}

def update_disk_info():
    """在后台更新磁盘信息"""
    try:
        partitions = []
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                partitions.append({
                    'device': partition.device,
                    'mountpoint': partition.mountpoint,
                    'fstype': partition.fstype,
                    'total': round(usage.total / (1024**3), 2),
                    'used': round(usage.used / (1024**3), 2),
                    'free': round(usage.free / (1024**3), 2),
                    'percent': usage.percent
                })
            except:
                continue

        io = psutil.disk_io_counters()
        io_info = {
            'read_bytes': round(io.read_bytes / (1024**2), 2),
            'write_bytes': round(io.write_bytes / (1024**2), 2),
            'read_count': io.read_count,
            'write_count': io.write_count
        }

        info = {
            'partitions': partitions,
            'io': io_info
        }
        with cache_lock:
            system_info_cache['disk'] = info
    except Exception as e:
        with cache_lock:
            system_info_cache['disk'] = {'error': str(e)}

def get_network_interfaces():
    """根据操作系统获取网络接口信息"""
    system = platform.system().lower()

    try:
        # 获取所有网络接口
        net_if_addrs = psutil.net_if_addrs()
        net_if_stats = psutil.net_if_stats()

        # 根据操作系统设置过滤条件
        if system == 'windows':
            # Windows 特定的过滤条件
            excluded_prefixes = ('lo', 'docker', 'veth', 'br-', 'vmware', 'vethernet')
            active_interfaces = {
                name: stats for name, stats in net_if_stats.items()
                if stats.isup and not any(name.lower().startswith(prefix) for prefix in excluded_prefixes)
            }
        elif system == 'linux':
            # Linux 特定的过滤条件
            excluded_prefixes = ('lo', 'docker', 'veth', 'br-', 'tun', 'tap')
            active_interfaces = {
                name: stats for name, stats in net_if_stats.items()
                if stats.isup and not any(name.lower().startswith(prefix) for prefix in excluded_prefixes)
            }
        else:
            # 其他操作系统使用通用过滤条件
            active_interfaces = {
                name: stats for name, stats in net_if_stats.items()
                if stats.isup
            }

        interfaces = []
        for interface_name, stats in active_interfaces.items():
            try:
                addresses = []
                if interface_name in net_if_addrs:
                    for addr in net_if_addrs[interface_name]:
                        if addr.family == socket.AF_INET:  # IPv4 地址
                            address_info = {
                                'address': addr.address,
                                'netmask': addr.netmask,
                                'broadcast': getattr(addr, 'broadcast', None)
                            }
                            # 根据操作系统添加特定信息
                            if system == 'windows':
                                address_info['type'] = 'IPv4'
                            elif system == 'linux':
                                address_info['scope'] = getattr(addr, 'scope', None)
                            addresses.append(address_info)

                if addresses:  # 只添加有 IP 地址的接口
                    interface_info = {
                        'name': interface_name,
                        'addresses': addresses,
                        'speed': stats.speed,
                        'mtu': stats.mtu
                    }
                    # 根据操作系统添加特定信息
                    if system == 'windows':
                        interface_info['description'] = getattr(stats, 'description', '')
                    elif system == 'linux':
                        interface_info['flags'] = getattr(stats, 'flags', 0)
                    interfaces.append(interface_info)
            except Exception as e:
                logger.warning(f"处理网络接口 {interface_name} 时出错: {str(e)}")
                continue

        return interfaces
    except Exception as e:
        logger.error(f"获取网络接口信息时出错: {str(e)}")
        return []

def get_network_io_stats():
    """获取网络 IO 统计信息"""
    try:
        io = psutil.net_io_counters()
        return {
            'bytes_sent': round(io.bytes_sent / (1024**2), 2),
            'bytes_recv': round(io.bytes_recv / (1024**2), 2),
            'packets_sent': io.packets_sent,
            'packets_recv': io.packets_recv,
            'errin': io.errin,
            'errout': io.errout,
            'dropin': io.dropin,
            'dropout': io.dropout
        }
    except Exception as e:
        logger.warning(f"获取网络 IO 统计信息时出错: {str(e)}")
        return {
            'bytes_sent': 0,
            'bytes_recv': 0,
            'packets_sent': 0,
            'packets_recv': 0,
            'errin': 0,
            'errout': 0,
            'dropin': 0,
            'dropout': 0
        }

def update_network_info():
    """在后台更新网络信息"""
    try:
        interfaces = get_network_interfaces()
        io_info = get_network_io_stats()

        info = {
            'interfaces': interfaces,
            'io': io_info
        }
        with cache_lock:
            system_info_cache['network'] = info
    except Exception as e:
        logger.error(f"更新网络信息时出错: {str(e)}")
        with cache_lock:
            system_info_cache['network'] = {
                'interfaces': [],
                'io': {
                    'bytes_sent': 0,
                    'bytes_recv': 0,
                    'packets_sent': 0,
                    'packets_recv': 0,
                    'errin': 0,
                    'errout': 0,
                    'dropin': 0,
                    'dropout': 0
                },
                'error': str(e)
            }

def update_processes():
    """在后台更新进程信息"""
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'create_time']):
            try:
                pinfo = proc.info
                pinfo['create_time'] = datetime.datetime.fromtimestamp(pinfo['create_time']).strftime('%Y-%m-%d %H:%M:%S')
                processes.append(pinfo)
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
        processes = processes[:20]

        with cache_lock:
            system_info_cache['processes'] = processes
    except Exception as e:
        with cache_lock:
            system_info_cache['processes'] = {'error': str(e)}

def update_all_info():
    """触发系统信息更新"""
    update_event.set()  # 设置事件，通知后台线程执行更新

@monitor_bp.route('/monitor')
@login_required
def dashboard():
    return render_template('monitor/dashboard.html')

@monitor_bp.route('/api/monitor/system')
@login_required
def get_system_info():
    update_all_info()  # 触发更新
    with cache_lock:
        return jsonify({'success': True, 'data': system_info_cache.get('system', {})})

@monitor_bp.route('/api/monitor/cpu')
@login_required
def get_cpu_info():
    update_all_info()  # 触发更新
    with cache_lock:
        return jsonify({'success': True, 'data': system_info_cache.get('cpu', {})})

@monitor_bp.route('/api/monitor/memory')
@login_required
def get_memory_info():
    update_all_info()  # 触发更新
    with cache_lock:
        return jsonify({'success': True, 'data': system_info_cache.get('memory', {})})

@monitor_bp.route('/api/monitor/disk')
@login_required
def get_disk_info():
    update_all_info()  # 触发更新
    with cache_lock:
        return jsonify({'success': True, 'data': system_info_cache.get('disk', {})})

@monitor_bp.route('/api/monitor/network')
@login_required
def get_network_info():
    update_all_info()  # 触发更新
    with cache_lock:
        return jsonify({'success': True, 'data': system_info_cache.get('network', {})})

@monitor_bp.route('/api/monitor/processes')
@login_required
def get_processes():
    update_all_info()  # 触发更新
    with cache_lock:
        return jsonify({'success': True, 'data': system_info_cache.get('processes', {})})
