#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA任务状态管理模块
定义OTA任务的详细状态和状态转换逻辑
"""

from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import Optional


class OtaTaskDetailedStatus(Enum):
    """OTA任务详细状态枚举"""
    PENDING = "等待中"
    INITIALIZING = "初始化中"
    CONNECTING = "连接设备中"
    LOADING_FIRMWARE = "加载固件中"
    STARTING_OTA = "启动OTA中"
    CHECKING_DIFF = "差分校验中"
    SENDING_SLICES = "发送固件分片中"
    QUERYING_RESULT = "查询结果中"
    REBOOTING = "重启设备中"
    SUCCESS = "成功"
    FAILED = "失败"
    CANCELLED = "已取消"
    PAUSED = "已暂停"


class OtaErrorType(Enum):
    """OTA错误类型枚举"""
    NETWORK_ERROR = "网络错误"
    DEVICE_ERROR = "设备错误"
    FIRMWARE_ERROR = "固件错误"
    SYSTEM_ERROR = "系统错误"
    TIMEOUT_ERROR = "超时错误"
    VALIDATION_ERROR = "验证错误"


@dataclass
class OtaTaskProgress:
    """OTA任务进度信息"""
    current: int = 0
    total: int = 100
    percentage: int = 0
    stage: str = ""
    message: str = ""

    def update(self, current: int, total: int, message: str = ""):
        """更新进度信息"""
        self.current = current
        self.total = total
        self.percentage = int((current / total) * 100) if total > 0 else 0
        self.message = message

    def set_stage(self, stage: OtaTaskDetailedStatus, message: str = ""):
        """设置当前阶段"""
        self.stage = stage.value
        if message:
            self.message = message


@dataclass
class OtaTaskError:
    """OTA任务错误信息"""
    error_type: OtaErrorType
    error_code: str
    error_message: str
    stage: OtaTaskDetailedStatus
    timestamp: datetime
    is_retryable: bool = True

    def __str__(self):
        return f"[{self.error_type.value}] {self.error_message} (阶段: {self.stage.value})"


class OtaTaskStateManager:
    """OTA任务状态管理器"""

    def __init__(self, task_id: int):
        self.task_id = task_id
        self.status = OtaTaskDetailedStatus.PENDING
        self.progress = OtaTaskProgress()
        self.error: Optional[OtaTaskError] = None
        self.retry_count = 0
        self.max_retries = 3
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None

    def start_task(self):
        """开始任务"""
        self.started_at = datetime.now()
        self.set_status(OtaTaskDetailedStatus.INITIALIZING, "任务开始执行")

    def complete_task(self, success: bool, message: str = ""):
        """完成任务"""
        self.completed_at = datetime.now()
        if success:
            self.set_status(OtaTaskDetailedStatus.SUCCESS, message or "任务执行成功")
            self.progress.update(100, 100, "升级完成")
        else:
            self.set_status(OtaTaskDetailedStatus.FAILED, message or "任务执行失败")

    def set_status(self, status: OtaTaskDetailedStatus, message: str = ""):
        """设置任务状态"""
        self.status = status
        self.progress.set_stage(status, message)

    def set_error(self, error_type: OtaErrorType, error_code: str,
                  error_message: str, is_retryable: bool = True):
        """设置错误信息"""
        self.error = OtaTaskError(
            error_type=error_type,
            error_code=error_code,
            error_message=error_message,
            stage=self.status,
            timestamp=datetime.now(),
            is_retryable=is_retryable
        )
        self.set_status(OtaTaskDetailedStatus.FAILED, str(self.error))

    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return (self.retry_count < self.max_retries and
                self.error is not None and
                self.error.is_retryable)

    def increment_retry(self):
        """增加重试次数"""
        self.retry_count += 1
        self.error = None  # 清除之前的错误信息

    def get_legacy_status(self) -> str:
        """获取兼容旧系统的状态 - 使用统一工具"""
        from utils.ota_common import StatusUtils
        return StatusUtils.get_legacy_status(self.status.name)

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'status': self.status.value,
            'legacy_status': self.get_legacy_status(),
            'progress': {
                'current': self.progress.current,
                'total': self.progress.total,
                'percentage': self.progress.percentage,
                'stage': self.progress.stage,
                'message': self.progress.message
            },
            'error': {
                'type': self.error.error_type.value if self.error else None,
                'code': self.error.error_code if self.error else None,
                'message': self.error.error_message if self.error else None,
                'is_retryable': self.error.is_retryable if self.error else None
            } if self.error else None,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
