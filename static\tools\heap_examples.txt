# Keil Microlib 堆内存分析工具示例数据

## 示例1: 初始状态堆内存
HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20002DB4,108
HEAP_FREE:0x20002E80,0x200050AC,8748
HEAP_ALLOC:0x20002DB4,0x20002E80,204
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END

## 示例2: 运行一段时间后的堆内存
HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20002DB4,108
HEAP_FREE:0x20002E80,0x200050AC,8748
HEAP_ALLOC:0x20002DB4,0x20002E80,204
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END

## 示例3: 内存碎片化严重的堆
HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_ALLOC:0x20002D48,0x20002D80,56
HEAP_FREE:0x20002D80,0x20002DB4,52
HEAP_ALLOC:0x20002DB4,0x20002DE8,52
HEAP_FREE:0x20002DE8,0x20002E1C,52
HEAP_ALLOC:0x20002E1C,0x20002E50,52
HEAP_FREE:0x20002E50,0x20002E84,52
HEAP_ALLOC:0x20002E84,0x20002EB8,52
HEAP_FREE:0x20002EB8,0x200050AC,8244
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END

## 示例4: 大块内存分配
HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_ALLOC:0x20002D48,0x20004000,4792
HEAP_FREE:0x20004000,0x20004100,256
HEAP_ALLOC:0x20004100,0x200050AC,4012
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END
