{% extends "base.html" %}

{% block title %}静态页面管理 - 充电桩管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-code text-primary me-2"></i>静态页面管理
                    </h3>
                </div>
                <div class="card-body">
                    <!-- 页面状态 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="devicesStatus">检查中...</h4>
                                            <p class="mb-0">设备静态页面</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-microchip fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-secondary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>即将推出</h4>
                                            <p class="mb-0">更多静态页面</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-plus fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作面板 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">页面操作</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="refreshStatus()">
                                            <i class="fas fa-sync-alt me-1"></i>刷新状态
                                        </button>
                                        <button type="button" class="btn btn-outline-success" onclick="generatePage('devices')">
                                            <i class="fas fa-cog me-1"></i>生成设备页面
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="viewPage('devices')">
                                            <i class="fas fa-eye me-1"></i>查看设备页面
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" onclick="generateAllPages()">
                                            <i class="fas fa-magic me-1"></i>生成所有页面
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 页面详情 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">页面详情</h5>
                                </div>
                                <div class="card-body">
                                    <div id="pageDetails">
                                        <div class="text-center">
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 通知容器 -->
<div id="notificationContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    refreshStatus();
    
    // 每60秒自动刷新状态
    setInterval(refreshStatus, 60000);
});

// 刷新页面状态
function refreshStatus() {
    fetch('/api/static/status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatusDisplay(data.status);
                updatePageDetails(data.status);
            } else {
                showNotification('获取页面状态失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('获取页面状态失败:', error);
            showNotification('获取页面状态失败', 'error');
        });
}

// 更新状态显示
function updateStatusDisplay(status) {
    const devicesStatus = document.getElementById('devicesStatus');
    
    if (status.devices.exists) {
        if (status.devices.fresh) {
            devicesStatus.textContent = '✓ 最新';
            devicesStatus.parentElement.parentElement.parentElement.className = 'card bg-success text-white';
        } else {
            devicesStatus.textContent = '⚠ 过期';
            devicesStatus.parentElement.parentElement.parentElement.className = 'card bg-warning text-white';
        }
    } else {
        devicesStatus.textContent = '✗ 不存在';
        devicesStatus.parentElement.parentElement.parentElement.className = 'card bg-danger text-white';
    }
}

// 更新页面详情
function updatePageDetails(status) {
    const html = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>页面</th>
                        <th>状态</th>
                        <th>文件大小</th>
                        <th>修改时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <i class="fas fa-microchip me-2"></i>设备页面
                        </td>
                        <td>
                            ${getStatusBadge(status.devices)}
                        </td>
                        <td>
                            ${status.devices.size ? formatFileSize(status.devices.size) : '-'}
                        </td>
                        <td>
                            ${status.devices.modified ? formatDate(status.devices.modified) : '-'}
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="generatePage('devices')">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="viewPage('devices')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById('pageDetails').innerHTML = html;
}

// 获取状态徽章
function getStatusBadge(pageStatus) {
    if (!pageStatus.exists) {
        return '<span class="badge bg-danger">不存在</span>';
    } else if (pageStatus.fresh) {
        return '<span class="badge bg-success">最新</span>';
    } else {
        return '<span class="badge bg-warning">过期</span>';
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化日期
function formatDate(timestamp) {
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
}

// 生成单个页面
function generatePage(pageName) {
    showNotification('正在生成页面...', 'info');
    
    fetch('/api/static/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ pages: [pageName] })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const result = data.results[pageName];
            if (result.success) {
                showNotification('页面生成成功', 'success');
                refreshStatus();
            } else {
                showNotification('页面生成失败: ' + result.message, 'error');
            }
        } else {
            showNotification('生成失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('生成页面失败:', error);
        showNotification('生成页面失败', 'error');
    });
}

// 查看页面
function viewPage(pageName) {
    const url = `/${pageName}_static`;
    window.open(url, '_blank');
}

// 生成所有页面
function generateAllPages() {
    showNotification('正在生成所有页面...', 'info');
    
    fetch('/api/static/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ pages: ['devices'] })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let successCount = 0;
            let totalCount = 0;
            
            for (const [page, result] of Object.entries(data.results)) {
                totalCount++;
                if (result.success) {
                    successCount++;
                }
            }
            
            showNotification(`生成完成: ${successCount}/${totalCount} 个页面成功`, 'success');
            refreshStatus();
        } else {
            showNotification('批量生成失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('批量生成失败:', error);
        showNotification('批量生成失败', 'error');
    });
}

// 显示通知
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    const iconClass = type === 'success' ? 'fa-check-circle' : 
                     type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show`;
    notification.innerHTML = `
        <i class="fas ${iconClass} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.getElementById('notificationContainer').appendChild(notification);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
{% endblock %}
