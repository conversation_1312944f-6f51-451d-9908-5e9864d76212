#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用工厂模块
用于创建和配置Flask应用
"""

import os
from flask import Flask
from flask_login import LoginManager

from config import Config, config
from utils.logger import LoggerManager
from utils.socket_manager import init_socketio
from sqlalchemy.orm import scoped_session, sessionmaker

# 获取日志记录器
logger = LoggerManager.get_logger()

# 初始化扩展，但不绑定到应用
from models.database import db

def create_app(config_name=None):
    """创建Flask应用"""
    # 根据环境变量或参数选择配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
        logger.info(f"使用配置：{config_name}")

    config_class = config.get(config_name, Config)

    # 创建应用
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 确保必要的目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['FIRMWARE_UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['INFLUXDB_DATA_PATH'], exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance'), exist_ok=True)

    # 初始化数据库
    db.init_app(app)

    # 在应用上下文中优化数据库会话配置
    with app.app_context():
        # 检查是否使用PostgreSQL
        is_postgresql = 'postgresql' in app.config['SQLALCHEMY_DATABASE_URI']

        if is_postgresql:
            # PostgreSQL环境：使用scoped_session支持多线程
            db.session = scoped_session(
                sessionmaker(bind=db.engine, autocommit=False, autoflush=False)
            )
            logger.info("已配置PostgreSQL多线程会话管理")
        else:
            # SQLite环境：保持默认配置
            logger.info("使用SQLite默认会话配置")


    # 初始化缓存
    from services.cache_service import init_cache
    if init_cache(app):
        logger.info("缓存服务初始化成功")
    else:
        logger.warning("缓存服务初始化失败，但应用将继续运行")

    # 初始化SocketIO
    init_socketio(app)

    # 初始化登录管理器
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'

    # 用户加载回调
    from models.user import User
    @login_manager.user_loader
    def load_user(user_id):
        try:
            return User.query.get(int(user_id))
        except Exception as e:
            logger.error(f"加载用户失败: {e}")
            return None

    # 初始化设备状态服务
    from services.device_status_service import init_device_status_service
    init_device_status_service(app)

    # 初始化调试脚本自动重启服务
    from services.auto_restart_service import auto_restart_service
    auto_restart_service.start_auto_restart(app.app_context())

    # 初始化IoT客户端管理器（确保在应用启动时就初始化）
    try:
        from services.iot_client_manager import IoTClientManager
        IoTClientManager.initialize()
        logger.info("IoT客户端管理器初始化成功")
    except Exception as e:
        logger.error(f"IoT客户端管理器初始化异常: {e}")

    # 初始化自动OTA升级服务
    try:
        from services.auto_ota_service import initialize_auto_ota_service
        if initialize_auto_ota_service(app):
            logger.info("自动OTA升级服务初始化成功")
        else:
            logger.warning("自动OTA升级服务初始化失败")
    except Exception as e:
        logger.error(f"自动OTA升级服务初始化异常: {e}")
        logger.warning("自动OTA升级服务将不可用")

    # 初始化InfluxDB服务
    with app.app_context():
        try:
            from services.influxdb_service import influxdb_service
            if influxdb_service.connect():
                logger.info("InfluxDB服务初始化成功")
            else:
                logger.warning("InfluxDB服务初始化失败，但应用将继续运行")
        except Exception as e:
            logger.error(f"InfluxDB服务初始化异常: {e}")
            logger.warning("应用将继续运行，但InfluxDB相关功能可能不可用")

    # 注册蓝图
    register_blueprints(app)

    # 注册应用关闭处理器
    @app.teardown_appcontext
    def shutdown_session(exception=None):
        """应用上下文关闭时清理数据库会话"""
        try:
            from services.database_session_manager import db_session_manager
            db_session_manager.remove_session()
        except Exception as e:
            logger.warning(f"清理数据库会话失败: {e}")

    # 注册应用关闭处理器
    import atexit
    def cleanup_on_exit():
        """应用退出时的清理工作"""
        try:
            from services.ota_service import shutdown_ota_service
            shutdown_ota_service()
            logger.info("应用退出清理完成")
        except Exception as e:
            logger.error(f"应用退出清理失败: {e}")

    atexit.register(cleanup_on_exit)

    return app

def register_blueprints(app):
    """注册所有蓝图"""
    # 导入蓝图
    from routes.auth import auth_bp
    from routes.user_routes import user_bp
    from routes.monitor_routes import monitor_bp
    from routes.game import game_bp
    from routes.tools import tools_bp
    from routes.login_logs import login_logs_bp
    from routes.device_parameters import device_parameters_bp
    from routes.firmware import firmware_bp
    from routes.device import device_bp
    from routes.device_location import bp as device_location_bp
    from routes.ai_routes import ai_bp
    from routes.paid_download_routes import paid_download_bp
    from routes.merchant import merchant_bp
    from routes.model_viewer import model_viewer_bp
    from routes.debug_script_routes import debug_script_bp
    from routes.ota_task import ota_task_bp
    from routes.device_console import device_console_bp

    # 导入新创建的蓝图
    from routes.main_routes import main_bp
    from routes.device_status_routes import device_status_bp
    from routes.iot_routes import iot_bp
    from routes.mqtt_forwarder_routes import mqtt_forwarder_bp
    from routes.batch_ota import batch_ota_bp
    from routes.auto_ota import auto_ota_bp
    from routes.register_config_routes import register_config_bp
    from routes.time_series_api_routes import time_series_api_bp
    from routes.config_management import config_management_bp
    from routes.wireless_transmitter import wireless_transmitter_bp
    from routes.code_management import code_management_bp
    from routes.cache_management import cache_management_bp
    from routes.static_pages import static_pages_bp

    # 注册蓝图
    app.register_blueprint(auth_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(monitor_bp)
    app.register_blueprint(game_bp)
    app.register_blueprint(tools_bp)
    app.register_blueprint(device_location_bp)
    app.register_blueprint(login_logs_bp)
    app.register_blueprint(device_parameters_bp)
    app.register_blueprint(firmware_bp)
    app.register_blueprint(device_bp)
    app.register_blueprint(ai_bp)
    app.register_blueprint(paid_download_bp)
    app.register_blueprint(merchant_bp)
    app.register_blueprint(model_viewer_bp)
    app.register_blueprint(debug_script_bp)
    app.register_blueprint(ota_task_bp)
    app.register_blueprint(device_console_bp)

    # 注册新创建的蓝图
    app.register_blueprint(main_bp)
    app.register_blueprint(device_status_bp)
    app.register_blueprint(iot_bp)
    app.register_blueprint(mqtt_forwarder_bp)
    app.register_blueprint(batch_ota_bp)
    app.register_blueprint(auto_ota_bp)
    app.register_blueprint(register_config_bp)
    app.register_blueprint(time_series_api_bp)
    app.register_blueprint(config_management_bp)
    app.register_blueprint(wireless_transmitter_bp)
    app.register_blueprint(code_management_bp)
    app.register_blueprint(cache_management_bp)
    app.register_blueprint(static_pages_bp)

def init_db(app):
    """初始化数据库"""
    with app.app_context():
        db.create_all()

        # 创建管理员用户（如果不存在）
        from models.user import User
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(username='admin', is_admin=True)
            admin.set_password('admin')
            db.session.add(admin)
            db.session.commit()
            logger.info('已创建管理员用户')
        # else:
        #     # 如果管理员用户已存在，更新密码哈希方法
        #     admin.set_password('admin')
        #     db.session.commit()
        #     logger.info('已更新管理员用户密码哈希方法')

        # 初始化异步任务管理器
        try:
            from services.async_task_manager import init_task_manager
            init_task_manager()
            logger.info('异步任务管理器初始化完成')
        except Exception as e:
            logger.error(f'异步任务管理器初始化失败: {e}')
