#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备状态管理路由模块
处理设备状态查询和管理
"""

from datetime import datetime
from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required

from models.device import Device
from services.iot_client_manager import IoTClientManager
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
device_status_bp = Blueprint('device_status', __name__)

@device_status_bp.route('/api/device_status/interval', methods=['POST'])
@login_required
def set_status_check_interval():
    """设置设备状态查询间隔"""
    try:
        data = request.get_json()
        if not data or 'interval' not in data:
            return jsonify({'success': False, 'message': '缺少interval参数'})

        interval_str = data['interval']
        device_status_thread = current_app.config.get('DEVICE_STATUS_THREAD')

        if device_status_thread.set_interval(interval_str):
            return jsonify({'success': True, 'message': f'状态查询间隔已设置为 {interval_str}'})
        else:
            return jsonify({'success': False, 'message': f'无效的时间间隔: {interval_str}'})
    except Exception as e:
        logger.error(f"设置状态查询间隔失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@device_status_bp.route('/api/device_status/interval', methods=['GET'])
@login_required
def get_status_check_interval():
    """获取设备状态查询间隔"""
    device_status_thread = current_app.config.get('DEVICE_STATUS_THREAD')
    return jsonify({'interval': device_status_thread.interval})

@device_status_bp.route('/api/device_status/start', methods=['POST'])
@login_required
def start_status_check():
    """启动设备状态查询"""
    device_status_thread = current_app.config.get('DEVICE_STATUS_THREAD')
    device_status_thread.start_thread()
    return jsonify({'success': True, 'message': '设备状态查询已启动'})

@device_status_bp.route('/api/device_status/stop', methods=['POST'])
@login_required
def stop_status_check():
    """停止设备状态查询"""
    device_status_thread = current_app.config.get('DEVICE_STATUS_THREAD')
    device_status_thread.stop_thread()
    return jsonify({'success': True, 'message': '设备状态查询已停止'})

@device_status_bp.route('/api/device_status')
@login_required
def get_device_status():
    """获取所有设备的状态（从缓存中读取）"""
    try:
        # 从应用上下文获取设备状态缓存
        device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
        device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')

        if not device_status_lock:
            logger.warning("设备状态锁未初始化")
            return jsonify({})

        with device_status_lock:
            # 确保所有设备都有状态记录
            devices = Device.query.all()
            current_timestamp = int(datetime.now().timestamp())

            # 为没有状态的设备添加默认状态
            for device in devices:
                if device.id not in device_status_cache:
                    device_status_cache[device.id] = {
                        'is_online': False,
                        'last_check': current_timestamp,
                    }

            # 转换为前端需要的格式，确保键为字符串类型
            response_data = {}
            for device_id, status in device_status_cache.items():
                # 确保device_id是字符串类型，便于前端处理
                key = str(device_id)
                response_data[key] = {
                    'is_online': bool(status.get('is_online', False)),
                    'last_check': datetime.fromtimestamp(status.get('last_check', current_timestamp)).strftime('%Y-%m-%d %H:%M:%S'),
                    'last_online_time': datetime.fromtimestamp(status.get('last_online', status.get('last_check', current_timestamp))).strftime('%Y-%m-%d %H:%M:%S') if status.get('is_online') else '未知'
                }

            logger.info(f"获取设备状态成功，共 {len(response_data)} 个设备状态")
            return jsonify(response_data)

    except Exception as e:
        logger.error(f"获取设备状态缓存失败: {e}")
        return jsonify({'error': str(e)}), 500
