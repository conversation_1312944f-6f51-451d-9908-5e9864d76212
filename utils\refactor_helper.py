#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重构辅助工具
用于自动化代码重构任务
"""

import os
import re
from pathlib import Path
from typing import List, Tuple


class RefactorHelper:
    """重构辅助类"""
    
    def __init__(self, project_root: str = '.'):
        self.project_root = Path(project_root)
    
    def clean_whitespace(self, file_path: str) -> bool:
        """清理文件中的多余空白"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除行尾空白
            lines = content.split('\n')
            cleaned_lines = [line.rstrip() for line in lines]
            
            # 移除文件末尾的多余空行
            while cleaned_lines and not cleaned_lines[-1]:
                cleaned_lines.pop()
            
            # 重新组合内容
            cleaned_content = '\n'.join(cleaned_lines)
            if cleaned_content and not cleaned_content.endswith('\n'):
                cleaned_content += '\n'
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            return True
            
        except Exception as e:
            print(f"清理文件 {file_path} 失败: {e}")
            return False
    
    def remove_meaningless_try_except(self, file_path: str) -> bool:
        """移除无意义的try-except块"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找简单的try-except模式
            # 这种模式只是重新包装异常而不做任何有意义的处理
            pattern = r'(\s+)try:\s*\n((?:\1    .*\n)*)\1except Exception as e:\s*\n(\1    .*\n)*\1    return False\s*\n'
            
            def replace_try_except(match):
                indent = match.group(1)
                try_body = match.group(2)
                
                # 如果try块中没有复杂逻辑，直接返回try块内容
                if try_body and not ('raise' in try_body or 'logger.error' in try_body):
                    # 移除try块的额外缩进
                    lines = try_body.split('\n')
                    cleaned_lines = []
                    for line in lines:
                        if line.strip():
                            # 移除一级缩进
                            if line.startswith(indent + '    '):
                                cleaned_lines.append(line[4:])
                            else:
                                cleaned_lines.append(line)
                    
                    return '\n'.join(cleaned_lines)
                
                return match.group(0)  # 保持原样
            
            # 应用替换
            new_content = re.sub(pattern, replace_try_except, content, flags=re.MULTILINE)
            
            if new_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                return True
            
            return False
            
        except Exception as e:
            print(f"处理文件 {file_path} 失败: {e}")
            return False
    
    def extract_validation_logic(self, file_path: str) -> List[str]:
        """提取重复的验证逻辑"""
        validations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找常见的验证模式
            patterns = [
                r'if not .*\.exists\(.*\):',
                r'if not .*\.is_running\(\):',
                r'if .*is None:',
                r'if not .*:',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content)
                validations.extend(matches)
            
        except Exception as e:
            print(f"分析文件 {file_path} 失败: {e}")
        
        return validations
    
    def find_duplicate_imports(self, file_path: str) -> List[str]:
        """查找重复的导入"""
        duplicates = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            imports = []
            for line in lines:
                line = line.strip()
                if line.startswith(('import ', 'from ')):
                    if line in imports:
                        duplicates.append(line)
                    else:
                        imports.append(line)
            
        except Exception as e:
            print(f"分析文件 {file_path} 失败: {e}")
        
        return duplicates
    
    def refactor_file(self, file_path: str) -> dict:
        """重构单个文件"""
        results = {
            'whitespace_cleaned': False,
            'try_except_removed': False,
            'duplicates_found': [],
            'validations_found': []
        }
        
        # 清理空白
        results['whitespace_cleaned'] = self.clean_whitespace(file_path)
        
        # 移除无意义的try-except
        results['try_except_removed'] = self.remove_meaningless_try_except(file_path)
        
        # 查找重复导入
        results['duplicates_found'] = self.find_duplicate_imports(file_path)
        
        # 提取验证逻辑
        results['validations_found'] = self.extract_validation_logic(file_path)
        
        return results
    
    def refactor_project(self, file_patterns: List[str] = None) -> dict:
        """重构整个项目"""
        if file_patterns is None:
            file_patterns = ['**/*.py']
        
        results = {
            'files_processed': 0,
            'files_modified': 0,
            'total_issues': 0
        }
        
        for pattern in file_patterns:
            for file_path in self.project_root.glob(pattern):
                # 跳过虚拟环境和缓存目录
                if any(part in str(file_path) for part in ['venv', '__pycache__', '.git']):
                    continue
                
                print(f"处理文件: {file_path}")
                file_results = self.refactor_file(str(file_path))
                
                results['files_processed'] += 1
                
                if any([
                    file_results['whitespace_cleaned'],
                    file_results['try_except_removed'],
                    file_results['duplicates_found'],
                    file_results['validations_found']
                ]):
                    results['files_modified'] += 1
                
                results['total_issues'] += len(file_results['duplicates_found'])
                results['total_issues'] += len(file_results['validations_found'])
        
        return results


def main():
    """主函数"""
    refactor = RefactorHelper()
    
    print("开始代码重构...")
    results = refactor.refactor_project(['services/*.py', 'routes/*.py'])
    
    print(f"\n重构完成:")
    print(f"处理文件数: {results['files_processed']}")
    print(f"修改文件数: {results['files_modified']}")
    print(f"发现问题数: {results['total_issues']}")


if __name__ == '__main__':
    main()
