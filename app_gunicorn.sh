#!/bin/bash
# 定义日志文件路径
LOG_FILE="output.log"

# 清空旧日志文件
# > "$LOG_FILE"

echo "正在启动 OTA 设备管理系统（后台模式）…"
echo "日志输出在 $LOG_FILE"

# 设置环境变量
export FLASK_ENV=production
export DATABASE_URL="postgresql://kafanglinlin:7jbWNHYZZLMa@localhost:5432/KafangCharging"

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 检查Gunicorn是否安装
if ! command -v gunicorn &> /dev/null; then
    echo "Gunicorn未安装，正在安装..."
    pip install gunicorn
fi

# 启动参数
HOST=${HOST:-0.0.0.0}
PORT=${PORT:-5000}
WORKERS=${WORKERS:-4}

echo "使用Gunicorn启动应用..."
echo "地址: http://$HOST:$PORT"
echo "工作进程数: $WORKERS"

# 启动Gunicorn（后台模式）
nohup gunicorn \
    --bind $HOST:$PORT \
    --workers $WORKERS \
    --worker-class eventlet \
    --timeout 120 \
    --keep-alive 2 \
    --max-requests 1000 \
    --preload \
    --access-logfile logs/access.log \
    --error-logfile logs/error.log \
    --log-level info \
    app:app > "$LOG_FILE" 2>&1 &

# 获取进程ID
PID=$!
echo "服务已启动，进程ID为 $PID"
echo "已启动，日志输出在 $LOG_FILE"
echo "服务地址: http://$HOST:$PORT"
echo "调试模式: 关闭"