# JSON编辑器组件使用说明

## 概述

本项目已集成了基于 `vanilla-jsoneditor` 的JSON编辑器组件，提供了强大的JSON编辑功能，包括语法高亮、验证、格式化等特性。

## 特性

- ✅ **语法高亮**: 自动高亮JSON语法
- ✅ **实时验证**: 输入时自动验证JSON格式
- ✅ **格式化**: 一键格式化JSON内容
- ✅ **复制功能**: 快速复制JSON内容到剪贴板
- ✅ **多种模式**: 支持文本模式和树形模式
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **错误提示**: 详细的错误信息和位置提示

## 文件结构

```
static/
├── libs/
│   ├── js/
│   │   └── vanilla-jsoneditor.min.js    # JSON编辑器库
│   └── css/
│       └── vanilla-jsoneditor.min.css   # JSON编辑器样式
└── js/
    └── components/
        └── json-editor.js               # JSON编辑器组件封装

templates/
└── components/
    └── json_editor.html                 # JSON编辑器模板组件
```

## 使用方法

### 1. 在模板中使用

在你的HTML模板中包含JSON编辑器组件：

```html
{% include 'components/json_editor.html' with {
    'editor_id': 'myEditor',
    'label': '配置内容 (JSON格式)',
    'height': '400px',
    'mode': 'text',
    'read_only': false,
    'show_validation': true,
    'validation_id': 'myValidation'
} %}
```

### 2. 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `editor_id` | string | 'jsonEditor' | 编辑器容器ID |
| `label` | string | 'JSON内容' | 编辑器标签 |
| `height` | string | '400px' | 编辑器高度 |
| `mode` | string | 'text' | 编辑器模式 ('text', 'tree') |
| `read_only` | boolean | false | 是否只读 |
| `show_validation` | boolean | true | 是否显示验证结果 |
| `validation_id` | string | editor_id + 'Validation' | 验证结果容器ID |

### 3. JavaScript API

#### 获取编辑器实例

```javascript
const editor = window.jsonEditors['myEditor'];
```

#### 设置内容

```javascript
// 设置JSON对象
editor.setContent({
    "name": "test",
    "value": 123
});

// 设置JSON字符串
editor.setContent('{"name": "test", "value": 123}');
```

#### 获取内容

```javascript
// 获取JSON对象
const jsonData = editor.getJSON();

// 获取JSON文本
const textData = editor.getText();

// 获取完整内容
const content = editor.getContent();
```

#### 验证和格式化

```javascript
// 验证JSON
const errors = editor.validate();

// 格式化JSON
editor.format();
```

#### 设置模式

```javascript
// 设置为文本模式
editor.setMode('text');

// 设置为树形模式
editor.setMode('tree');
```

#### 设置只读

```javascript
// 设置为只读
editor.setReadOnly(true);

// 设置为可编辑
editor.setReadOnly(false);
```

### 4. 事件监听

```javascript
// 监听内容变化
editor.on('change', function(updatedContent, previousContent, { contentErrors }) {
    console.log('内容已更改');
    if (contentErrors && contentErrors.length > 0) {
        console.log('验证错误:', contentErrors);
    }
});

// 监听错误
editor.on('error', function(errors) {
    console.log('编辑器错误:', errors);
});
```

### 5. 工具栏功能

编辑器自带工具栏，包含以下功能：

- **格式化**: 格式化JSON内容
- **验证**: 验证JSON格式
- **复制**: 复制JSON内容到剪贴板
- **模式切换**: 在文本模式和树形模式之间切换

## 示例

### 基本使用示例

```html
<!-- 在模板中 -->
{% include 'components/json_editor.html' with {
    'editor_id': 'configEditor',
    'label': '配置文件',
    'height': '500px'
} %}

<script>
// 页面加载完成后
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        const editor = window.jsonEditors['configEditor'];
        if (editor) {
            // 设置初始数据
            editor.setContent({
                "server": {
                    "host": "localhost",
                    "port": 8080
                },
                "database": {
                    "type": "mysql",
                    "host": "localhost",
                    "port": 3306
                }
            });
        }
    }, 200);
});

// 保存配置
function saveConfig() {
    const editor = window.jsonEditors['configEditor'];
    if (editor) {
        try {
            const config = editor.getJSON();
            // 发送到服务器
            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });
        } catch (error) {
            alert('JSON格式错误: ' + error.message);
        }
    }
}
</script>
```

## 已更新的页面

以下页面已经更新为使用新的JSON编辑器组件：

1. **配置管理页面** (`templates/config_management.html`)
   - 路径: `/config-management`
   - 功能: 服务器产品配置管理

2. **简化配置页面** (`templates/config_simple.html`)
   - 路径: `/config-simple`
   - 功能: 产品配置管理（简化版）

3. **测试页面** (`templates/test_json_editor.html`)
   - 路径: `/test-json-editor`
   - 功能: JSON编辑器功能测试

## 兼容性

- 支持现代浏览器 (Chrome, Firefox, Safari, Edge)
- 自动降级支持：如果JSON编辑器库加载失败，会自动降级为普通的textarea
- 响应式设计，支持移动设备

## 故障排除

### 1. 编辑器不显示

检查浏览器控制台是否有错误信息：
- 确保 `vanilla-jsoneditor.min.js` 文件已正确加载
- 确保 `json-editor.js` 组件文件已正确加载

### 2. 样式问题

检查CSS文件是否正确加载：
- 确保 `vanilla-jsoneditor.min.css` 文件已正确加载

### 3. 功能异常

检查JavaScript错误：
- 打开浏览器开发者工具查看控制台错误
- 确保页面加载完成后再初始化编辑器

## 更新日志

- **2025-01-24**: 初始版本，集成vanilla-jsoneditor库
- **2025-01-24**: 创建可复用的JSON编辑器组件
- **2025-01-24**: 更新配置管理页面使用新组件
- **2025-01-24**: 添加测试页面和文档
