/**
 * JSON编辑器组件
 * 基于 vanilla-jsoneditor 库的封装
 * 提供语法高亮、验证、格式化等功能
 */

class JSONEditorComponent {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.editor = null;
        this.options = {
            mode: 'text', // 'text', 'tree', 'table'
            mainMenuBar: true,
            navigationBar: true,
            statusBar: true,
            askToFormat: false,
            readOnly: false,
            indentation: 2,
            tabSize: 2,
            ...options
        };
        
        this.callbacks = {
            onChange: options.onChange || null,
            onError: options.onError || null,
            onValidate: options.onValidate || null
        };
        
        this.init();
    }
    
    /**
     * 初始化编辑器
     */
    init() {
        if (!this.container) {
            console.error(`Container with id "${this.containerId}" not found`);
            return;
        }
        
        if (typeof JSONEditor === 'undefined') {
            console.error('vanilla-jsoneditor library not loaded');
            return;
        }
        
        try {
            // 创建编辑器实例
            this.editor = new JSONEditor({
                target: this.container,
                props: {
                    ...this.options,
                    onChange: (updatedContent, previousContent, { contentErrors, patchResult }) => {
                        // 处理内容变化
                        if (this.callbacks.onChange) {
                            this.callbacks.onChange(updatedContent, previousContent, { contentErrors, patchResult });
                        }
                        
                        // 处理验证错误
                        if (contentErrors && contentErrors.length > 0) {
                            if (this.callbacks.onError) {
                                this.callbacks.onError(contentErrors);
                            }
                        }
                    },
                    onError: (error) => {
                        console.error('JSON Editor Error:', error);
                        if (this.callbacks.onError) {
                            this.callbacks.onError([error]);
                        }
                    }
                }
            });
            
            console.log('JSON Editor initialized successfully');
        } catch (error) {
            console.error('Failed to initialize JSON Editor:', error);
        }
    }
    
    /**
     * 设置JSON内容
     * @param {Object|string} content - JSON内容
     */
    setContent(content) {
        if (!this.editor) return;
        
        try {
            let jsonContent;
            if (typeof content === 'string') {
                jsonContent = { text: content };
            } else {
                jsonContent = { json: content };
            }
            
            this.editor.set(jsonContent);
        } catch (error) {
            console.error('Failed to set content:', error);
        }
    }
    
    /**
     * 获取JSON内容
     * @returns {Object|string} JSON内容
     */
    getContent() {
        if (!this.editor) return null;
        
        try {
            const content = this.editor.get();
            return content;
        } catch (error) {
            console.error('Failed to get content:', error);
            return null;
        }
    }
    
    /**
     * 获取JSON文本
     * @returns {string} JSON文本
     */
    getText() {
        if (!this.editor) return '';
        
        try {
            const content = this.editor.get();
            if (content.text !== undefined) {
                return content.text;
            } else if (content.json !== undefined) {
                return JSON.stringify(content.json, null, this.options.indentation);
            }
            return '';
        } catch (error) {
            console.error('Failed to get text:', error);
            return '';
        }
    }
    
    /**
     * 获取JSON对象
     * @returns {Object} JSON对象
     */
    getJSON() {
        if (!this.editor) return null;
        
        try {
            const content = this.editor.get();
            if (content.json !== undefined) {
                return content.json;
            } else if (content.text !== undefined) {
                return JSON.parse(content.text);
            }
            return null;
        } catch (error) {
            console.error('Failed to get JSON:', error);
            return null;
        }
    }
    
    /**
     * 验证JSON内容
     * @returns {Array} 验证错误数组
     */
    validate() {
        if (!this.editor) return [];
        
        try {
            const content = this.editor.get();
            const errors = [];
            
            // 如果是文本模式，尝试解析JSON
            if (content.text !== undefined) {
                try {
                    JSON.parse(content.text);
                } catch (parseError) {
                    errors.push({
                        type: 'error',
                        message: 'Invalid JSON: ' + parseError.message,
                        line: this.getErrorLine(parseError.message, content.text)
                    });
                }
            }
            
            return errors;
        } catch (error) {
            console.error('Failed to validate:', error);
            return [{ type: 'error', message: error.message }];
        }
    }
    
    /**
     * 格式化JSON内容
     */
    format() {
        if (!this.editor) return;
        
        try {
            const content = this.editor.get();
            if (content.text !== undefined) {
                const parsed = JSON.parse(content.text);
                const formatted = JSON.stringify(parsed, null, this.options.indentation);
                this.setContent(formatted);
            }
        } catch (error) {
            console.error('Failed to format:', error);
        }
    }
    
    /**
     * 设置只读模式
     * @param {boolean} readOnly - 是否只读
     */
    setReadOnly(readOnly) {
        this.options.readOnly = readOnly;
        if (this.editor) {
            this.editor.updateProps({ readOnly });
        }
    }
    
    /**
     * 设置模式
     * @param {string} mode - 编辑器模式 ('text', 'tree', 'table')
     */
    setMode(mode) {
        this.options.mode = mode;
        if (this.editor) {
            this.editor.updateProps({ mode });
        }
    }
    
    /**
     * 销毁编辑器
     */
    destroy() {
        if (this.editor) {
            this.editor.destroy();
            this.editor = null;
        }
    }
    
    /**
     * 获取错误行号
     * @param {string} errorMessage - 错误消息
     * @param {string} text - JSON文本
     * @returns {number} 行号
     */
    getErrorLine(errorMessage, text) {
        // 尝试从错误消息中提取行号
        const lineMatch = errorMessage.match(/line (\d+)/i);
        if (lineMatch) {
            return parseInt(lineMatch[1]);
        }
        
        // 尝试从错误消息中提取位置
        const positionMatch = errorMessage.match(/position (\d+)/i);
        if (positionMatch) {
            const position = parseInt(positionMatch[1]);
            const lines = text.substring(0, position).split('\n');
            return lines.length;
        }
        
        return 1;
    }
    
    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (event === 'change') {
            this.callbacks.onChange = callback;
        } else if (event === 'error') {
            this.callbacks.onError = callback;
        } else if (event === 'validate') {
            this.callbacks.onValidate = callback;
        }
    }
    
    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     */
    off(event) {
        if (event === 'change') {
            this.callbacks.onChange = null;
        } else if (event === 'error') {
            this.callbacks.onError = null;
        } else if (event === 'validate') {
            this.callbacks.onValidate = null;
        }
    }
}

// 全局工厂函数
window.createJSONEditor = function(containerId, options = {}) {
    return new JSONEditorComponent(containerId, options);
};

// 导出类
window.JSONEditorComponent = JSONEditorComponent;
