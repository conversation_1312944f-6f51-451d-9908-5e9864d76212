<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>堆内存工具测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .test-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Keil Microlib堆内存分析工具测试</h1>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>这个页面提供了一些测试数据，你可以复制这些数据到堆内存分析工具中进行测试。</p>
            <a href="http://localhost:5000/tools/heap_visualizer" class="btn" target="_blank">🚀 打开堆内存分析工具</a>
        </div>

        <div class="test-section">
            <h3>📊 示例1: 初始状态堆内存</h3>
            <p>这是一个典型的初始状态堆内存布局，有少量的分配和大量的空闲空间。</p>
            <div class="test-data">HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20002DB4,108
HEAP_FREE:0x20002E80,0x200050AC,8748
HEAP_ALLOC:0x20002DB4,0x20002E80,204
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END</div>
            <button onclick="copyToClipboard('example1')" class="btn">📋 复制数据</button>
        </div>

        <div class="test-section">
            <h3>📊 示例2: 内存碎片化严重</h3>
            <p>这个示例展示了内存碎片化严重的情况，有多个小的分配和空闲块交替出现。</p>
            <div class="test-data">HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_ALLOC:0x20002D48,0x20002D80,56
HEAP_FREE:0x20002D80,0x20002DB4,52
HEAP_ALLOC:0x20002DB4,0x20002DE8,52
HEAP_FREE:0x20002DE8,0x20002E1C,52
HEAP_ALLOC:0x20002E1C,0x20002E50,52
HEAP_FREE:0x20002E50,0x20002E84,52
HEAP_ALLOC:0x20002E84,0x20002EB8,52
HEAP_FREE:0x20002EB8,0x200050AC,8244
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END</div>
            <button onclick="copyToClipboard('example2')" class="btn">📋 复制数据</button>
        </div>

        <div class="test-section">
            <h3>📊 示例3: 大块内存分配</h3>
            <p>这个示例展示了大块内存分配的情况，适合测试大内存块的可视化效果。</p>
            <div class="test-data">HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_ALLOC:0x20002D48,0x20004000,4792
HEAP_FREE:0x20004000,0x20004100,256
HEAP_ALLOC:0x20004100,0x200050AC,4012
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END</div>
            <button onclick="copyToClipboard('example3')" class="btn">📋 复制数据</button>
        </div>

        <div class="test-section">
            <h3>📊 示例4: 混合使用模式</h3>
            <p>这个示例展示了混合使用模式，包含不同大小的分配和空闲块。</p>
            <div class="test-data">HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20003000,2744
HEAP_ALLOC:0x20003000,0x20003500,1280
HEAP_FREE:0x20003500,0x20004000,2816
HEAP_ALLOC:0x20004000,0x20004800,2048
HEAP_FREE:0x20004800,0x200050AC,2220
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END</div>
            <button onclick="copyToClipboard('example4')" class="btn">📋 复制数据</button>
        </div>

        <div class="test-section">
            <h3>🎯 使用说明</h3>
            <ol>
                <li>点击上方的"打开堆内存分析工具"按钮</li>
                <li><strong>新功能：</strong>点击"示例数据"折叠菜单，选择预设示例</li>
                <li>或者复制下方任意一个示例数据到输入框</li>
                <li>设置一个有意义的名称（如"初始状态"、"碎片化测试"等）</li>
                <li>点击"添加堆数据"按钮</li>
                <li>重复步骤2-5添加多个堆数据进行对比</li>
                <li><strong>新功能：</strong>使用风格选择器切换不同的可视化风格</li>
                <li>鼠标悬浮在不同的内存块上查看详细信息（高亮效果）</li>
                <li><strong>新功能：</strong>使用滚轮缩放或点击+/-按钮调整视图</li>
                <li>拖拽画布来平移视图</li>
                <li>使用导出功能保存可视化结果</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🆕 最新功能亮点</h3>
            <ul>
                <li>🎨 <strong>9种可视化风格</strong>：渐变、扁平、霓虹、经典、玻璃、材质、赛博朋克、极简、复古</li>
                <li>📁 <strong>美观折叠菜单</strong>：优雅的示例数据选择界面</li>
                <li>🔍 <strong>智能缩放平移</strong>：支持滚轮缩放和拖拽平移，每个堆独立状态</li>
                <li>✨ <strong>增强悬浮效果</strong>：小块放大显示，详细信息提示</li>
                <li>📐 <strong>超高分辨率</strong>：1800x400高清画布，支持高DPI显示</li>
                <li>🎯 <strong>智能小块处理</strong>：小内存块自动放大，扩大点击区域</li>
                <li>📊 <strong>数据自动排序</strong>：按地址自动排序，检测间隙和重叠</li>
                <li>🎪 <strong>网格和标尺</strong>：辅助网格线和详细地址标签</li>
                <li>📈 <strong>内存使用率条</strong>：实时显示内存使用情况</li>
                <li>🔧 <strong>数据验证</strong>：自动检测和修复数据问题</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎨 风格预览</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>🌈 渐变风格</strong><br>
                    <small>经典渐变色彩，温和过渡</small>
                </div>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>📱 扁平风格</strong><br>
                    <small>现代扁平化设计，简洁明了</small>
                </div>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>💫 霓虹风格</strong><br>
                    <small>炫酷发光效果，科技感十足</small>
                </div>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>🏛️ 经典风格</strong><br>
                    <small>3D立体效果，怀旧经典</small>
                </div>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>🔮 玻璃风格</strong><br>
                    <small>半透明玻璃质感，优雅精致</small>
                </div>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>🎯 材质风格</strong><br>
                    <small>Material Design，现代阴影</small>
                </div>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>🤖 赛博朋克</strong><br>
                    <small>未来科幻风，扫描线效果</small>
                </div>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>⚪ 极简风格</strong><br>
                    <small>纯线条设计，极致简约</small>
                </div>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <strong>🕹️ 复古风格</strong><br>
                    <small>像素化效果，8位游戏风</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        const examples = {
            'example1': `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20002DB4,108
HEAP_FREE:0x20002E80,0x200050AC,8748
HEAP_ALLOC:0x20002DB4,0x20002E80,204
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`,
            'example2': `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_ALLOC:0x20002D48,0x20002D80,56
HEAP_FREE:0x20002D80,0x20002DB4,52
HEAP_ALLOC:0x20002DB4,0x20002DE8,52
HEAP_FREE:0x20002DE8,0x20002E1C,52
HEAP_ALLOC:0x20002E1C,0x20002E50,52
HEAP_FREE:0x20002E50,0x20002E84,52
HEAP_ALLOC:0x20002E84,0x20002EB8,52
HEAP_FREE:0x20002EB8,0x200050AC,8244
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`,
            'example3': `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_ALLOC:0x20002D48,0x20004000,4792
HEAP_FREE:0x20004000,0x20004100,256
HEAP_ALLOC:0x20004100,0x200050AC,4012
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`,
            'example4': `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20003000,2744
HEAP_ALLOC:0x20003000,0x20003500,1280
HEAP_FREE:0x20003500,0x20004000,2816
HEAP_ALLOC:0x20004000,0x20004800,2048
HEAP_FREE:0x20004800,0x200050AC,2220
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`
        };

        function copyToClipboard(exampleId) {
            const text = examples[exampleId];
            navigator.clipboard.writeText(text).then(() => {
                alert('数据已复制到剪贴板！');
            }).catch(err => {
                console.error('复制失败:', err);
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('数据已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
