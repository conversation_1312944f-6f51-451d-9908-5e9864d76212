#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
InfluxDB服务模块
用于连接InfluxDB Edge并存储/查询时序数据
使用嵌入式模式，无需外部InfluxDB服务器
"""

import os
import json
import csv
import logging
from datetime import datetime, timedelta
from typing import Optional, Any, Dict, List
from flask import current_app
from utils.logger import LoggerManager
import mmap
import threading
from functools import lru_cache

# 获取日志记录器
logger = LoggerManager.get_logger()

class InfluxDBService:
    """InfluxDB服务类，提供时序数据存储和查询功能，使用嵌入式模式"""

    _instance = None

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(InfluxDBService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化InfluxDB服务"""
        if self._initialized:
            return

        self._initialized = True
        self.client = None
        self.write_api = None
        self.query_api = None
        self.connected = False
        self.db_path = None
        self._cache = {}
        self._cache_lock = threading.Lock()
        self._cache_ttl = 300  # 缓存过期时间（秒）

        # 不在初始化时连接，而是在需要时才连接

    def connect(self) -> bool:
        """
        连接到InfluxDB Edge（嵌入式模式）

        Returns:
            bool: 连接是否成功
        """
        try:
            # 从配置中获取InfluxDB数据路径
            self.db_path = current_app.config.get('INFLUXDB_DATA_PATH')
            bucket = current_app.config.get('INFLUXDB_BUCKET')

            if not self.db_path or not bucket:
                logger.error("InfluxDB配置不完整")
                return False

            # 确保数据目录存在
            os.makedirs(self.db_path, exist_ok=True)

            # 创建数据文件目录
            bucket_path = os.path.join(self.db_path, bucket)
            os.makedirs(bucket_path, exist_ok=True)

            # 标记为已连接 - 我们将使用自定义文件存储方式
            self.connected = True
            self.bucket = bucket
            logger.info(f"InfluxDB Edge连接成功，数据存储在: {self.db_path}")
            return True

        except Exception as e:
            logger.error(f"InfluxDB连接异常: {e}")
            self.connected = False
            return False

    def write_sensor_data(self, device_id: str, channel_powers: list[float],
                      voltage: Optional[float], temperature: Optional[float], total_power: Optional[float],
                      csq: Optional[int], ber: Optional[int], bl0910_error_count: Optional[int] = None,
                      relay_state: Optional[int] = None, relay_bits: Optional[dict] = None,
                      short_period_error_count: Optional[int] = None,
                      long_period_error_count: Optional[int] = None,
                      zero_cross_time: Optional[int] = None) -> bool:
        """
        写入传感器数据到文件系统

        Args:
            device_id: 设备ID
            channel_powers: 10个通道的功率值列表
            voltage: 电压有效值
            temperature: 温度
            total_power: 总有功功率
            csq: 信号质量
            ber: 误码率
            bl0910_error_count: BL0910错误计数
            relay_state: 继电器状态
            relay_bits: 继电器状态位字典
            short_period_error_count: 短周期错误计数
            long_period_error_count: 长周期错误计数
            zero_cross_time: 零交叉时间

        Returns:
            bool: 写入是否成功
        """
        if not self.connected:
            if not self.connect():
                return False

        try:
            # 获取当前时间
            now = datetime.now()
            timestamp = now.isoformat()

            # 创建设备目录
            device_dir = os.path.join(self.db_path, self.bucket, device_id)
            os.makedirs(device_dir, exist_ok=True)

            # 创建日期目录 (按年月日组织)
            date_dir = os.path.join(device_dir, now.strftime('%Y-%m-%d'))
            os.makedirs(date_dir, exist_ok=True)

            # 写入功率数据
            if channel_powers:
                self._write_power_data(date_dir, timestamp, channel_powers)

            # 写入温度数据
            if temperature is not None:
                self._write_temperature_data(date_dir, timestamp, temperature)

            # 写入电压数据
            if voltage is not None:
                self._write_voltage_data(date_dir, timestamp, voltage)

            # 写入信号质量数据
            if csq is not None or ber is not None:
                self._write_csq_data(date_dir, timestamp, csq, ber)

            # 写入总功率数据
            if total_power is not None:
                self._write_total_power_data(date_dir, timestamp, total_power)

            # 写入BL0910错误计数
            if bl0910_error_count is not None:
                self._write_single_value_data(date_dir, timestamp, 'bl0910_error_count', bl0910_error_count)

            # 写入继电器状态
            if relay_state is not None:
                self._write_single_value_data(date_dir, timestamp, 'relay_state', relay_state)

            # 写入继电器状态位
            if relay_bits is not None:
                self._write_relay_bits_data(date_dir, timestamp, relay_bits)

            # 写入短周期错误计数
            if short_period_error_count is not None:
                self._write_single_value_data(date_dir, timestamp, 'short_period_error_count', short_period_error_count)

            # 写入长周期错误计数
            if long_period_error_count is not None:
                self._write_single_value_data(date_dir, timestamp, 'long_period_error_count', long_period_error_count)

            # 写入零交叉时间
            if zero_cross_time is not None:
                self._write_single_value_data(date_dir, timestamp, 'zero_cross_time', zero_cross_time)

            logger.debug(f"设备 {device_id} 的传感器数据已写入文件")
            return True
        except Exception as e:
            logger.error(f"写入传感器数据异常: {e}")
            return False

    def _write_power_data(self, date_dir: str, timestamp: str, channel_powers: list[float]) -> bool:
        """
        写入功率数据到文件系统

        Args:
            date_dir: 日期目录
            timestamp: 时间戳
            channel_powers: 10个通道的功率值列表

        Returns:
            bool: 写入是否成功
        """
        try:
            # 创建数据文件名 (使用小时作为文件名)
            hour_file = os.path.join(date_dir, f"power_{datetime.fromisoformat(timestamp).strftime('%H')}.csv")

            # 准备数据行
            data_row = [timestamp]
            for power in channel_powers:
                data_row.append(str(float(power)))

            # 检查文件是否存在
            file_exists = os.path.isfile(hour_file)

            # 写入数据
            with open(hour_file, 'a', newline='') as f:
                writer = csv.writer(f)

                # 如果文件不存在，写入表头
                if not file_exists:
                    header = ['timestamp']
                    for i in range(len(channel_powers)):
                        header.append(f'channel_{i+1}')
                    writer.writerow(header)

                # 写入数据行
                writer.writerow(data_row)

            return True
        except Exception as e:
            logger.error(f"写入功率数据异常: {e}")
            return False

    def _write_temperature_data(self, date_dir: str, timestamp: str, temperature: float) -> bool:
        """
        写入温度数据到文件系统

        Args:
            date_dir: 日期目录
            timestamp: 时间戳
            temperature: 温度值

        Returns:
            bool: 写入是否成功
        """
        try:
            # 创建数据文件名 (使用小时作为文件名)
            hour_file = os.path.join(date_dir, f"temperature_{datetime.fromisoformat(timestamp).strftime('%H')}.csv")

            # 准备数据行
            data_row = [timestamp, str(temperature)]

            # 检查文件是否存在
            file_exists = os.path.isfile(hour_file)

            # 写入数据
            with open(hour_file, 'a', newline='') as f:
                writer = csv.writer(f)

                # 如果文件不存在，写入表头
                if not file_exists:
                    header = ['timestamp', 'temperature']
                    writer.writerow(header)

                # 写入数据行
                writer.writerow(data_row)

            return True
        except Exception as e:
            logger.error(f"写入温度数据异常: {e}")
            return False

    def _write_voltage_data(self, date_dir: str, timestamp: str, voltage: float) -> bool:
        """
        写入电压数据到文件系统

        Args:
            date_dir: 日期目录
            timestamp: 时间戳
            voltage: 电压值

        Returns:
            bool: 写入是否成功
        """
        try:
            # 创建数据文件名 (使用小时作为文件名)
            hour_file = os.path.join(date_dir, f"voltage_{datetime.fromisoformat(timestamp).strftime('%H')}.csv")

            # 准备数据行
            data_row = [timestamp, str(voltage)]

            # 检查文件是否存在
            file_exists = os.path.isfile(hour_file)

            # 写入数据
            with open(hour_file, 'a', newline='') as f:
                writer = csv.writer(f)

                # 如果文件不存在，写入表头
                if not file_exists:
                    header = ['timestamp', 'voltage']
                    writer.writerow(header)

                # 写入数据行
                writer.writerow(data_row)

            return True
        except Exception as e:
            logger.error(f"写入电压数据异常: {e}")
            return False

    def _write_csq_data(self, date_dir: str, timestamp: str, csq: Optional[int], ber: Optional[int]) -> bool:
        """
        写入信号质量数据到文件系统

        Args:
            date_dir: 日期目录
            timestamp: 时间戳
            csq: 信号质量
            ber: 误码率

        Returns:
            bool: 写入是否成功
        """
        try:
            # 创建数据文件名 (使用小时作为文件名)
            hour_file = os.path.join(date_dir, f"csq_{datetime.fromisoformat(timestamp).strftime('%H')}.csv")

            # 准备数据行
            data_row = [timestamp, str(csq) if csq is not None else "", str(ber) if ber is not None else ""]

            # 检查文件是否存在
            file_exists = os.path.isfile(hour_file)

            # 写入数据
            with open(hour_file, 'a', newline='') as f:
                writer = csv.writer(f)

                # 如果文件不存在，写入表头
                if not file_exists:
                    header = ['timestamp', 'csq', 'ber']
                    writer.writerow(header)

                # 写入数据行
                writer.writerow(data_row)

            return True
        except Exception as e:
            logger.error(f"写入信号质量数据异常: {e}")
            return False

    def _write_total_power_data(self, date_dir: str, timestamp: str, total_power: float) -> bool:
        """
        写入总功率数据到文件系统

        Args:
            date_dir: 日期目录
            timestamp: 时间戳
            total_power: 总功率值

        Returns:
            bool: 写入是否成功
        """
        try:
            # 创建数据文件名 (使用小时作为文件名)
            hour_file = os.path.join(date_dir, f"total_power_{datetime.fromisoformat(timestamp).strftime('%H')}.csv")

            # 准备数据行
            data_row = [timestamp, str(total_power)]

            # 检查文件是否存在
            file_exists = os.path.isfile(hour_file)

            # 写入数据
            with open(hour_file, 'a', newline='') as f:
                writer = csv.writer(f)

                # 如果文件不存在，写入表头
                if not file_exists:
                    header = ['timestamp', 'total_power']
                    writer.writerow(header)

                # 写入数据行
                writer.writerow(data_row)

            return True
        except Exception as e:
            logger.error(f"写入总功率数据异常: {e}")
            return False

    def write_power_data(self, device_id: str, channel_powers: list[float]) -> bool:
        """
        写入功率数据到文件系统 (兼容旧版本)

        Args:
            device_id: 设备ID
            channel_powers: 10个通道的功率值列表

        Returns:
            bool: 写入是否成功
        """
        if not self.connected:
            if not self.connect():
                return False

        try:
            # 获取当前时间
            now = datetime.now()
            timestamp = now.isoformat()

            # 创建设备目录
            device_dir = os.path.join(self.db_path, self.bucket, device_id)
            os.makedirs(device_dir, exist_ok=True)

            # 创建日期目录 (按年月日组织)
            date_dir = os.path.join(device_dir, now.strftime('%Y-%m-%d'))
            os.makedirs(date_dir, exist_ok=True)

            # 创建数据文件名 (使用小时作为文件名)
            hour_file = os.path.join(date_dir, f"{now.strftime('%H')}.csv")

            # 准备数据行
            data_row = [timestamp]
            for power in channel_powers:
                data_row.append(str(float(power)))

            # 检查文件是否存在，不存在则创建并写入表头
            file_exists = os.path.isfile(hour_file)

            with open(hour_file, 'a', newline='') as f:
                writer = csv.writer(f)

                # 如果文件不存在，写入表头
                if not file_exists:
                    header = ['timestamp']
                    for i in range(len(channel_powers)):
                        header.append(f'channel_{i+1}')
                    writer.writerow(header)

                # 写入数据行
                writer.writerow(data_row)

            logger.debug(f"设备 {device_id} 的功率数据已写入文件: {hour_file}")
            return True

        except Exception as e:
            logger.error(f"写入功率数据异常: {e}")
            return False

    def query_power_data(self, device_id: str, start_time: datetime,
                        end_time: Optional[datetime] = None) -> Dict[str, List[Dict[str, Any]]]:
        """查询设备功率数据"""
        try:
            # 生成缓存键
            date_str = start_time.strftime('%Y-%m-%d')
            cache_key = self._get_cache_key(device_id, 'power', date_str)

            # 尝试从缓存获取数据
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data

            # 如果没有缓存，从文件读取
            device_dir = os.path.join(self.db_path, self.bucket, device_id)
            if not os.path.exists(device_dir):
                return {}

            result_data = {f"channel_{i+1}": [] for i in range(10)}
            date_dir = os.path.join(device_dir, date_str)

            if os.path.exists(date_dir):
                # 获取功率数据文件
                power_files = [f for f in os.listdir(date_dir) if f.startswith('power_') and f.endswith('.csv')]

                for hour_file in power_files:
                    file_path = os.path.join(date_dir, hour_file)
                    # 使用内存映射读取文件
                    lines = self._read_csv_file_mmap(file_path)

                    if not lines:
                        continue

                    header = lines[0]

                    for row in lines[1:]:
                        if len(row) <= 10:  # 确保有足够的数据列
                            continue

                        try:
                            timestamp = datetime.fromisoformat(row[0])
                            if start_time <= timestamp <= end_time:
                                for i in range(10):
                                    value = float(row[i+1])
                                    result_data[f"channel_{i+1}"].append({
                                        "time": timestamp.isoformat(),
                                        "value": value
                                    })
                        except (ValueError, IndexError):
                            continue

            # 缓存结果
            self._set_cache(cache_key, result_data)
            return result_data

        except Exception as e:
            logger.error(f"查询功率数据异常: {e}")
            return {}

    def query_temperature_data(self, device_id: str, start_time: datetime,
                           end_time: Optional[datetime] = None) -> list[dict[str, Any]]:
        """
        查询设备温度数据

        Args:
            device_id: 设备ID
            start_time: 开始时间
            end_time: 结束时间，如果为None则使用当前时间

        Returns:
            list: 包含温度数据的列表
        """
        return self._query_single_value_data(device_id, start_time, end_time, 'temperature')

    def query_voltage_data(self, device_id: str, start_time: datetime,
                        end_time: Optional[datetime] = None) -> list[dict[str, Any]]:
        """
        查询设备电压数据

        Args:
            device_id: 设备ID
            start_time: 开始时间
            end_time: 结束时间，如果为None则使用当前时间

        Returns:
            list: 包含电压数据的列表
        """
        return self._query_single_value_data(device_id, start_time, end_time, 'voltage')

    def query_total_power_data(self, device_id: str, start_time: datetime,
                            end_time: Optional[datetime] = None) -> list[dict[str, Any]]:
        """
        查询设备总功率数据

        Args:
            device_id: 设备ID
            start_time: 开始时间
            end_time: 结束时间，如果为None则使用当前时间

        Returns:
            list: 包含总功率数据的列表
        """
        return self._query_single_value_data(device_id, start_time, end_time, 'total_power')

    def query_csq_data(self, device_id: str, start_time: datetime,
                      end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """查询设备信号质量数据"""
        try:
            # 生成缓存键
            date_str = start_time.strftime('%Y-%m-%d')
            cache_key = self._get_cache_key(device_id, 'csq', date_str)

            # 尝试从缓存获取数据
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data

            # 如果没有缓存，从文件读取
            device_dir = os.path.join(self.db_path, self.bucket, device_id)
            if not os.path.exists(device_dir):
                return []

            result_data = []
            date_dir = os.path.join(device_dir, date_str)

            if os.path.exists(date_dir):
                # 获取信号质量数据文件
                csq_files = [f for f in os.listdir(date_dir) if f.startswith('csq_') and f.endswith('.csv')]

                for hour_file in csq_files:
                    file_path = os.path.join(date_dir, hour_file)
                    # 使用内存映射读取文件
                    lines = self._read_csv_file_mmap(file_path)

                    if not lines:
                        continue

                    header = lines[0]
                    csq_index = header.index('csq')
                    ber_index = header.index('ber')

                    for row in lines[1:]:
                        if len(row) <= max(csq_index, ber_index):
                            continue

                        try:
                            timestamp = datetime.fromisoformat(row[0])
                            if start_time <= timestamp <= end_time:
                                csq = int(row[csq_index])
                                ber = int(row[ber_index])
                                result_data.append({
                                    "time": timestamp.isoformat(),
                                    "csq": csq,
                                    "ber": ber
                                })
                        except (ValueError, IndexError):
                            continue

            # 缓存结果
            self._set_cache(cache_key, result_data)
            return result_data

        except Exception as e:
            logger.error(f"查询信号质量数据异常: {e}")
            return []

    def _write_single_value_data(self, date_dir: str, timestamp: str, data_type: str, value: Any) -> bool:
        """
        写入单值数据到文件系统

        Args:
            date_dir: 日期目录
            timestamp: 时间戳
            data_type: 数据类型
            value: 数据值

        Returns:
            bool: 写入是否成功
        """
        try:
            # 创建数据文件名 (使用小时作为文件名)
            hour_file = os.path.join(date_dir, f"{data_type}_{datetime.fromisoformat(timestamp).strftime('%H')}.csv")

            # 准备数据行
            data_row = [timestamp, str(value) if value is not None else ""]

            # 检查文件是否存在
            file_exists = os.path.isfile(hour_file)

            # 写入数据
            with open(hour_file, 'a', newline='') as f:
                writer = csv.writer(f)

                # 如果文件不存在，写入表头
                if not file_exists:
                    header = ['timestamp', data_type]
                    writer.writerow(header)

                # 写入数据行
                writer.writerow(data_row)

            return True
        except Exception as e:
            logger.error(f"写入{data_type}数据异常: {e}")
            return False

    def _write_relay_bits_data(self, date_dir: str, timestamp: str, relay_bits: dict) -> bool:
        """
        写入继电器状态位数据到文件系统

        Args:
            date_dir: 日期目录
            timestamp: 时间戳
            relay_bits: 继电器状态位字典

        Returns:
            bool: 写入是否成功
        """
        try:
            # 创建数据文件名 (使用小时作为文件名)
            hour_file = os.path.join(date_dir, f"relay_bits_{datetime.fromisoformat(timestamp).strftime('%H')}.csv")

            # 准备数据行
            data_row = [timestamp]

            # 获取所有可能的继电器位
            relay_keys = sorted([key for key in relay_bits.keys()])

            # 添加每个继电器位的值
            for key in relay_keys:
                data_row.append("1" if relay_bits.get(key, False) else "0")

            # 检查文件是否存在
            file_exists = os.path.isfile(hour_file)

            # 写入数据
            with open(hour_file, 'a', newline='') as f:
                writer = csv.writer(f)

                # 如果文件不存在，写入表头
                if not file_exists:
                    header = ['timestamp'] + relay_keys
                    writer.writerow(header)

                # 写入数据行
                writer.writerow(data_row)

            return True
        except Exception as e:
            logger.error(f"写入继电器状态位数据异常: {e}")
            return False

    def _get_cache_key(self, device_id: str, data_type: str, date: str) -> str:
        """生成缓存键"""
        return f"{device_id}:{data_type}:{date}"

    def _get_from_cache(self, cache_key: str) -> Optional[List[Dict[str, Any]]]:
        """从缓存获取数据"""
        with self._cache_lock:
            if cache_key in self._cache:
                cache_data = self._cache[cache_key]
                if datetime.now().timestamp() - cache_data['timestamp'] < self._cache_ttl:
                    return cache_data['data']
                else:
                    del self._cache[cache_key]
        return None

    def _set_cache(self, cache_key: str, data: List[Dict[str, Any]]):
        """设置缓存数据"""
        with self._cache_lock:
            self._cache[cache_key] = {
                'data': data,
                'timestamp': datetime.now().timestamp()
            }

    def _read_csv_file_mmap(self, file_path: str) -> List[List[str]]:
        """使用内存映射读取CSV文件"""
        try:
            with open(file_path, 'r') as f:
                # 创建内存映射
                mm = mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)
                # 读取所有行
                lines = []
                for line in iter(mm.readline, b''):
                    lines.append(line.decode('utf-8').strip().split(','))
                mm.close()
                return lines
        except Exception as e:
            logger.error(f"读取文件 {file_path} 异常: {e}")
            return []

    def _query_single_value_data(self, device_id: str, start_time: datetime,
                               end_time: Optional[datetime], data_type: str) -> List[Dict[str, Any]]:
        """查询单值数据（温度、电压等）"""
        try:
            # 生成缓存键
            date_str = start_time.strftime('%Y-%m-%d')
            cache_key = self._get_cache_key(device_id, data_type, date_str)

            # 尝试从缓存获取数据
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data

            # 如果没有缓存，从文件读取
            device_dir = os.path.join(self.db_path, self.bucket, device_id)
            if not os.path.exists(device_dir):
                return []

            result_data = []
            date_dir = os.path.join(device_dir, date_str)

            if os.path.exists(date_dir):
                # 获取该类型的数据文件
                data_files = [f for f in os.listdir(date_dir) if f.startswith(f'{data_type}_') and f.endswith('.csv')]

                for hour_file in data_files:
                    file_path = os.path.join(date_dir, hour_file)
                    # 使用内存映射读取文件
                    lines = self._read_csv_file_mmap(file_path)

                    if not lines:
                        continue

                    header = lines[0]
                    value_index = header.index(data_type)

                    for row in lines[1:]:
                        if len(row) <= value_index:
                            continue

                        try:
                            timestamp = datetime.fromisoformat(row[0])
                            if start_time <= timestamp <= end_time:
                                value = float(row[value_index])
                                result_data.append({
                                    "time": timestamp.isoformat(),
                                    "value": value
                                })
                        except (ValueError, IndexError):
                            continue

            # 缓存结果
            self._set_cache(cache_key, result_data)
            return result_data

        except Exception as e:
            logger.error(f"查询{data_type}数据异常: {e}")
            return []

# 创建全局实例
influxdb_service = InfluxDBService()
