#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
请求处理器管理系统
支持注册多个请求处理器，实现可扩展的请求处理架构
"""

import logging
from typing import Dict, Callable, List
from abc import ABC, abstractmethod

from iot_client.bin_block.bin_block import BinBlock
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class RequestHandler(ABC):
    """请求处理器基类"""

    @abstractmethod
    def get_supported_requests(self) -> List[int]:
        """返回支持的请求类型列表"""
        pass

    @abstractmethod
    def handle_request(self, device_id: int, session_id: int, request_msg: BinBlock) -> bool:
        """
        处理请求

        Args:
            device_id: 设备ID
            session_id: 会话ID
            request_msg: 请求消息

        Returns:
            bool: 是否成功处理
        """
        pass

    @property
    def name(self) -> str:
        """处理器名称"""
        return self.__class__.__name__


class RequestHandlerManager:
    """请求处理器管理器"""

    def __init__(self):
        self._handlers: Dict[int, List[RequestHandler]] = {}
        self._all_handlers: List[RequestHandler] = []

    def register_handler(self, handler: RequestHandler):
        """注册请求处理器"""
        try:
            supported_requests = handler.get_supported_requests()

            for request_type in supported_requests:
                if request_type not in self._handlers:
                    self._handlers[request_type] = []
                self._handlers[request_type].append(handler)

            self._all_handlers.append(handler)
            logger.info(f"已注册请求处理器: {handler.name}, 支持请求类型: {supported_requests}")

        except Exception as e:
            logger.error(f"注册请求处理器 {handler.name} 失败: {e}")

    def unregister_handler(self, handler: RequestHandler):
        """注销请求处理器"""
        try:
            if handler in self._all_handlers:
                self._all_handlers.remove(handler)

                # 从所有请求类型中移除该处理器
                for request_type, handlers in self._handlers.items():
                    if handler in handlers:
                        handlers.remove(handler)

                logger.info(f"已注销请求处理器: {handler.name}")

        except Exception as e:
            logger.error(f"注销请求处理器 {handler.name} 失败: {e}")

    def handle_request(self, device_id: int, session_id: int, request_msg: BinBlock):
        """处理请求消息"""
        try:
            request_type = request_msg.msgObj

            # 查找对应的处理器
            handlers = self._handlers.get(request_type, [])

            if not handlers:
                logger.debug(f"未找到请求类型 {request_type} 的处理器")
                return

            # 依次调用处理器
            handled = False
            for handler in handlers:
                try:
                    if handler.handle_request(device_id, session_id, request_msg):
                        handled = True
                        logger.debug(f"请求 {request_type} 已被处理器 {handler.name} 处理")
                except Exception as e:
                    logger.error(f"处理器 {handler.name} 处理请求时发生错误: {e}")

            if not handled:
                logger.debug(f"请求类型 {request_type} 未被任何处理器成功处理")

        except Exception as e:
            logger.error(f"处理请求消息时发生错误: {e}")

    def get_registered_handlers(self) -> List[RequestHandler]:
        """获取所有已注册的处理器"""
        return self._all_handlers.copy()

    def get_handlers_for_request(self, request_type: int) -> List[RequestHandler]:
        """获取指定请求类型的处理器"""
        return self._handlers.get(request_type, []).copy()


# 创建全局实例
request_handler_manager = RequestHandlerManager()


def register_request_handler(handler: RequestHandler):
    """注册请求处理器的便捷函数"""
    request_handler_manager.register_handler(handler)


def unregister_request_handler(handler: RequestHandler):
    """注销请求处理器的便捷函数"""
    request_handler_manager.unregister_handler(handler)
