from flask import Blueprint, render_template, request, jsonify, current_app
from flask_login import login_required, current_user
from models.database import db
from models.device import Device
from models.firmware import Firmware
from services.ota_service import start_ota_task
from services.iot_client_manager import IoTClientManager
from utils.logger import LoggerManager

logger = LoggerManager.get_logger()

batch_ota_bp = Blueprint('batch_ota', __name__)

@batch_ota_bp.route('/batch_ota')
@login_required
def batch_ota_page():
    """批量OTA页面 - 高级设备筛选器"""
    try:
        # 获取所有设备用于筛选
        devices = Device.query.all()

        # 获取固件列表
        firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()

        # 统计信息
        total_devices = len(devices)
        device_types = {}
        product_keys = set()

        for device in devices:
            # 统计设备类型
            device_type_name = device.device_type_name
            device_types[device_type_name] = device_types.get(device_type_name, 0) + 1

            # 收集产品密钥
            if device.product_key:
                product_keys.add(device.product_key)

        stats = {
            'total_devices': total_devices,
            'device_types': device_types,
            'product_keys': sorted(list(product_keys))
        }

        return render_template('batch_ota.html',
                             devices=devices,
                             firmwares=firmwares,
                             stats=stats)

    except Exception as e:
        logger.error(f"获取批量OTA页面失败: {e}")
        return render_template('batch_ota.html',
                             devices=[],
                             firmwares=[],
                             stats={'total_devices': 0, 'device_types': {}, 'product_keys': []},
                             error=str(e))

@batch_ota_bp.route('/api/batch_ota/filter_devices', methods=['POST'])
@login_required
def filter_devices():
    """根据筛选条件获取设备列表"""
    try:
        data = request.get_json() or {}

        # 获取筛选条件
        device_types = data.get('device_types', [])
        product_keys = data.get('product_keys', [])
        firmware_versions = data.get('firmware_versions', [])
        keywords = data.get('keywords', '').strip()
        exclude_keywords = data.get('exclude_keywords', '').strip()

        # 开始查询
        query = Device.query

        # 按设备类型筛选
        if device_types:
            type_values = []
            for type_name in device_types:
                if "V2" in type_name:
                    type_values.append(10)
                elif "V5" in type_name and "V51" not in type_name:
                    type_values.append(50)
                elif "V51" in type_name:
                    type_values.append(51)
            if type_values:
                query = query.filter(Device.device_type.in_(type_values))

        # 按产品密钥筛选
        if product_keys:
            query = query.filter(Device.product_key.in_(product_keys))

        # 按固件版本筛选
        if firmware_versions:
            query = query.filter(Device.firmware_version.in_(firmware_versions))

        # 按关键字筛选（包含）
        if keywords:
            keyword_list = [k.strip() for k in keywords.split(',') if k.strip()]
            for keyword in keyword_list:
                query = query.filter(
                    db.or_(
                        Device.device_id.ilike(f'%{keyword}%'),
                        Device.device_remark.ilike(f'%{keyword}%')
                    )
                )

        # 按关键字筛选（排除）
        if exclude_keywords:
            exclude_list = [k.strip() for k in exclude_keywords.split(',') if k.strip()]
            for keyword in exclude_list:
                query = query.filter(
                    ~db.or_(
                        Device.device_id.ilike(f'%{keyword}%'),
                        Device.device_remark.ilike(f'%{keyword}%')
                    )
                )

        # 执行查询
        devices = query.all()

        # 转换为JSON格式
        device_list = []
        for device in devices:
            device_list.append({
                'id': device.id,
                'device_id': device.device_id,
                'device_remark': device.device_remark or '',
                'product_key': device.product_key or '',
                'firmware_version': device.firmware_version or '未知',
                'device_type': device.device_type,
                'device_type_name': device.device_type_name,
                'last_ota_time': device.last_ota_time.strftime('%Y-%m-%d %H:%M:%S') if device.last_ota_time else '从未升级',
                'last_ota_status': device.last_ota_status or '未升级'
            })

        return jsonify({
            'success': True,
            'devices': device_list,
            'total': len(device_list)
        })

    except Exception as e:
        logger.error(f"筛选设备失败: {e}")
        return jsonify({
            'success': False,
            'message': f'筛选设备失败: {str(e)}',
            'devices': [],
            'total': 0
        }), 500

@batch_ota_bp.route('/api/batch_ota/start', methods=['POST'])
@login_required
def start_batch_ota():
    """启动批量OTA升级"""
    try:
        data = request.get_json() or {}
        device_ids = data.get('device_ids', [])

        if not device_ids:
            return jsonify({
                'success': False,
                'message': '请选择要升级的设备'
            }), 400

        # 检查IoT管理器是否启动
        if not IoTClientManager.is_running():
            return jsonify({
                'success': False,
                'message': 'IoT管理器未启动，无法执行OTA升级'
            }), 400

        # 获取设备信息并按类型分组
        devices = Device.query.filter(Device.id.in_(device_ids)).all()
        if not devices:
            return jsonify({
                'success': False,
                'message': '未找到指定的设备'
            }), 400

        # 按设备类型分组并获取对应的最新固件
        device_firmware_map = {}
        for device in devices:
            device_type = device.device_type
            if device_type not in device_firmware_map:
                # 获取该设备类型的最新固件
                latest_firmware = Firmware.query.filter_by(device_type=device_type).order_by(Firmware.upload_time.desc()).first()
                if latest_firmware:
                    device_firmware_map[device_type] = latest_firmware
                else:
                    logger.warning(f"设备类型 {device_type} 没有可用的固件")

        # 创建OTA任务
        success_count = 0
        failed_count = 0
        messages = []

        for device in devices:
            device_type = device.device_type
            if device_type in device_firmware_map:
                firmware = device_firmware_map[device_type]
                success, message = start_ota_task([device.id], firmware_id=firmware.id)
                if success:
                    success_count += 1
                    messages.append(f"设备 {device.device_id} 升级任务创建成功")
                else:
                    failed_count += 1
                    messages.append(f"设备 {device.device_id} 升级任务创建失败: {message}")
            else:
                failed_count += 1
                messages.append(f"设备 {device.device_id} 没有可用的固件")

        if success_count > 0:
            return jsonify({
                'success': True,
                'message': f'批量OTA升级任务创建完成，成功: {success_count}，失败: {failed_count}',
                'details': messages,
                'success_count': success_count,
                'failed_count': failed_count
            })
        else:
            return jsonify({
                'success': False,
                'message': f'所有设备的OTA升级任务创建失败',
                'details': messages,
                'success_count': success_count,
                'failed_count': failed_count
            }), 400

    except Exception as e:
        logger.error(f"启动批量OTA升级失败: {e}")
        return jsonify({
            'success': False,
            'message': f'启动批量OTA升级失败: {str(e)}'
        }), 500

@batch_ota_bp.route('/api/batch_ota/get_firmware_versions')
@login_required
def get_firmware_versions():
    """获取所有固件版本列表"""
    try:
        # 获取所有设备的固件版本（去重）
        result = db.session.query(Device.firmware_version).distinct().filter(
            Device.firmware_version.isnot(None),
            Device.firmware_version != '',
            Device.firmware_version != '未知'
        ).all()

        versions = [row[0] for row in result if row[0]]
        versions.sort()

        return jsonify({
            'success': True,
            'versions': versions
        })

    except Exception as e:
        logger.error(f"获取固件版本列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取固件版本列表失败: {str(e)}',
            'versions': []
        }), 500
