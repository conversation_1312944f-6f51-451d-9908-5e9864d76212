#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
静态页面路由
提供静态页面生成和访问功能
"""

import os
from flask import Blueprint, render_template, jsonify, send_file, request
from flask_login import login_required
from pathlib import Path

from services.static_page_service import static_page_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
static_pages_bp = Blueprint('static_pages', __name__)


@static_pages_bp.route('/devices_static')
@login_required
def devices_static():
    """静态设备页面 - 预加载数据的真正静态页面"""
    try:
        from datetime import datetime
        from models.device import Device
        from models.firmware import Firmware
        from models.database import db

        # 预加载设备数据
        devices = Device.query.order_by(Device.id.desc()).limit(100).all()

        # 预加载固件数据
        firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()

        # 获取设备统计
        total_devices = Device.query.count()
        type_stats = db.session.query(
            Device.device_type,
            db.func.count(Device.id).label('count')
        ).group_by(Device.device_type).all()

        # 构建统计数据
        stats = {
            'total_devices': total_devices,
            'online_devices': 0,  # 静态页面暂不显示在线状态
            'offline_devices': 0
        }

        return render_template('devices_static.html',
                             devices=devices,
                             firmwares=firmwares,
                             stats=stats,
                             is_static=True,
                             generated_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    except Exception as e:
        logger.error(f"访问静态设备页面失败: {e}")
        return render_template('error.html',
                             error_message=f"页面加载失败: {e}"), 500


@static_pages_bp.route('/api/static/refresh/devices', methods=['POST'])
@login_required
def refresh_devices_static():
    """刷新静态设备页面"""
    try:
        logger.info("手动刷新静态设备页面")
        success = static_page_service.generate_devices_static_page()
        
        if success:
            return jsonify({
                'success': True,
                'message': '静态页面刷新成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '静态页面生成失败'
            }), 500
            
    except Exception as e:
        logger.error(f"刷新静态设备页面失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@static_pages_bp.route('/api/static/status')
@login_required
def get_static_status():
    """获取静态页面状态"""
    try:
        devices_file = static_page_service.get_static_file_path('devices')
        devices_fresh = static_page_service.is_static_file_fresh('devices', max_age_minutes=30)
        
        status = {
            'devices': {
                'exists': devices_file is not None,
                'fresh': devices_fresh,
                'path': str(devices_file) if devices_file else None
            }
        }
        
        if devices_file:
            stat = devices_file.stat()
            status['devices']['size'] = stat.st_size
            status['devices']['modified'] = stat.st_mtime
        
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        logger.error(f"获取静态页面状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@static_pages_bp.route('/api/static/generate', methods=['POST'])
@login_required
def generate_static_pages():
    """批量生成静态页面"""
    try:
        pages = request.json.get('pages', ['devices'])
        results = {}
        
        for page in pages:
            if page == 'devices':
                success = static_page_service.generate_devices_static_page()
                results[page] = {
                    'success': success,
                    'message': '生成成功' if success else '生成失败'
                }
            else:
                results[page] = {
                    'success': False,
                    'message': f'不支持的页面类型: {page}'
                }
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        logger.error(f"批量生成静态页面失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@static_pages_bp.route('/static_pages_admin')
@login_required
def static_pages_admin():
    """静态页面管理界面"""
    return render_template('admin/static_pages.html')
