from flask import Blueprint, render_template, request, jsonify, current_app
from flask_login import login_required
import json
import os
from datetime import datetime
import shutil

config_management_bp = Blueprint("config_management", __name__)

CONFIG_FILE_PATH = "config/server_products.json"
BACKUP_DIR = "config/backups"

def ensure_backup_dir():
    """确保备份目录存在"""
    if not os.path.exists(BACKUP_DIR):
        os.makedirs(BACKUP_DIR)

def load_config():
    """加载配置文件"""
    try:
        with open(CONFIG_FILE_PATH, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        current_app.logger.error(f"加载配置文件失败: {e}")
        return None

def save_config(config_data):
    """保存配置文件"""
    try:
        # 创建备份
        ensure_backup_dir()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(BACKUP_DIR, f"server_products_{timestamp}.json")
        shutil.copy2(CONFIG_FILE_PATH, backup_path)

        # 保存新配置
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        # 重新加载配置到内存（如果有配置服务的话）
        reload_config_to_memory()

        return True
    except Exception as e:
        current_app.logger.error(f"保存配置文件失败: {e}")
        return False

def reload_config_to_memory():
    """重新加载配置到内存"""
    try:
        # 这里应该调用配置服务来重新加载配置
        # 目前只是一个占位符
        pass
    except Exception as e:
        current_app.logger.error(f"重新加载配置到内存失败: {e}")

@config_management_bp.route("/config-management")
@login_required
def config_management():
    """配置管理页面"""
    return render_template("config_management.html")

@config_management_bp.route("/config-simple")
@login_required
def config_simple():
    """简化配置管理页面"""
    return render_template("config_simple.html")

@config_management_bp.route("/api/config", methods=['GET'])
@login_required
def get_config():
    """获取配置 - 简化版本"""
    try:
        config = load_config()
        if config is None:
            return jsonify({"products": {}}), 200
        return jsonify(config), 200
    except Exception as e:
        current_app.logger.error(f"获取配置失败: {e}")
        return jsonify({"error": str(e)}), 500

@config_management_bp.route("/api/config", methods=['POST'])
@login_required
def update_config():
    """更新配置 - 简化版本"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "无效的JSON数据"}), 400

        success = save_config(data)
        if success:
            return jsonify({"success": True, "message": "配置保存成功"})
        else:
            return jsonify({"error": "保存配置失败"}), 500
    except Exception as e:
        current_app.logger.error(f"更新配置失败: {e}")
        return jsonify({"error": str(e)}), 500

@config_management_bp.route("/api/config/server-products", methods=['GET'])
@login_required
def get_server_products_config():
    """获取服务器产品配置"""
    try:
        config = load_config()
        if config is None:
            return jsonify({"success": False, "error": "无法加载配置文件"}), 500

        return jsonify({
            "success": True,
            "config": config
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@config_management_bp.route("/api/config/server-products", methods=['POST'])
@login_required
def update_server_products_config():
    """更新服务器产品配置"""
    try:
        data = request.get_json()
        if not data or 'config' not in data:
            return jsonify({"success": False, "error": "缺少配置数据"}), 400

        config = data['config']

        # 验证配置格式
        if not validate_config(config):
            return jsonify({"success": False, "error": "配置格式不正确"}), 400

        # 保存配置
        if save_config(config):
            return jsonify({"success": True, "message": "配置保存成功"})
        else:
            return jsonify({"success": False, "error": "保存配置失败"}), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

def validate_config(config):
    """验证配置格式"""
    try:
        # 检查必需的顶级键
        required_keys = ['server_types', 'products', 'migration_rules']
        for key in required_keys:
            if key not in config:
                return False

        # 检查server_types格式
        if not isinstance(config['server_types'], dict):
            return False

        # 检查products格式
        if not isinstance(config['products'], dict):
            return False

        # 检查migration_rules格式
        if not isinstance(config['migration_rules'], dict):
            return False

        return True
    except Exception:
        return False

@config_management_bp.route("/api/config/backups", methods=['GET'])
@login_required
def get_config_backups():
    """获取配置备份列表"""
    try:
        ensure_backup_dir()
        backups = []

        for filename in os.listdir(BACKUP_DIR):
            if filename.startswith("server_products_") and filename.endswith(".json"):
                filepath = os.path.join(BACKUP_DIR, filename)
                stat = os.stat(filepath)
                backups.append({
                    "filename": filename,
                    "size": stat.st_size,
                    "created_time": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                })

        # 按创建时间倒序排列
        backups.sort(key=lambda x: x['created_time'], reverse=True)

        return jsonify({
            "success": True,
            "backups": backups
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@config_management_bp.route("/api/config/restore/<backup_filename>", methods=['POST'])
@login_required
def restore_config_backup(backup_filename):
    """恢复配置备份"""
    try:
        backup_path = os.path.join(BACKUP_DIR, backup_filename)

        # 检查备份文件是否存在
        if not os.path.exists(backup_path):
            return jsonify({"success": False, "error": "备份文件不存在"}), 404

        # 加载备份配置
        with open(backup_path, 'r', encoding='utf-8') as f:
            backup_config = json.load(f)

        # 验证备份配置
        if not validate_config(backup_config):
            return jsonify({"success": False, "error": "备份配置格式不正确"}), 400

        # 保存配置（这会自动创建当前配置的备份）
        if save_config(backup_config):
            return jsonify({"success": True, "message": "配置恢复成功"})
        else:
            return jsonify({"success": False, "error": "恢复配置失败"}), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
