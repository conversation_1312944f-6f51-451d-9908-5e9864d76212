{% extends "base.html" %}

{% block title %}寄存器配置管理{% endblock %}

{% block styles %}
<style>
    .register-card {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    .register-card:hover {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-color: #86b7fe;
    }
    .register-header {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .register-body {
        padding: 1rem;
    }
    .category-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }
    .category-time {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
    .category-power {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .category-other {
        background-color: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }
    .editable-field {
        border: 1px solid transparent;
        background: transparent;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        width: 100%;
        transition: all 0.2s ease;
    }
    .editable-field:hover {
        background-color: #f8f9fa;
        border-color: #dee2e6;
    }
    .editable-field:focus {
        background-color: #fff;
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    .filter-card {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    .filter-header {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
    }
    .filter-body {
        padding: 1rem;
    }
    .register-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 1rem;
    }
    .register-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }
    .register-info-item {
        display: flex;
        flex-direction: column;
    }
    .register-info-label {
        font-size: 0.75rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
        font-weight: 500;
    }
    .register-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid #dee2e6;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    .search-box {
        position: relative;
    }
    .search-box .form-control {
        padding-left: 2.5rem;
    }
    .search-box .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-cogs text-primary me-2"></i>寄存器配置管理
            <small class="text-muted">设备寄存器参数配置</small>
        </h2>
        <div>
            <button type="button" class="btn btn-success" id="addRegisterBtn">
                <i class="fas fa-plus me-1"></i>添加寄存器
            </button>
            <button type="button" class="btn btn-primary" id="saveAllBtn">
                <i class="fas fa-save me-1"></i>保存所有更改
            </button>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <h4 class="mb-1">{{ stats.total }}</h4>
                    <p class="mb-0">总寄存器数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="mb-1 text-info">{{ stats.time }}</h4>
                    <p class="mb-0">时间类型</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="mb-1 text-danger">{{ stats.power }}</h4>
                    <p class="mb-0">功率类型</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="mb-1 text-secondary">{{ stats.other }}</h4>
                    <p class="mb-0">其他类型</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 筛选和搜索 -->
        <div class="col-md-3">
            <div class="filter-card">
                <div class="filter-header">
                    <i class="fas fa-filter me-2"></i>筛选和搜索
                </div>
                <div class="filter-body">
                    <!-- 搜索框 -->
                    <div class="mb-3">
                        <label class="form-label">搜索寄存器</label>
                        <div class="search-box">
                            <input type="text" class="form-control" id="searchInput" placeholder="地址、名称或描述">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>

                    <!-- 分类筛选 -->
                    <div class="mb-3">
                        <label class="form-label">分类筛选</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="categoryFilter" value="all" id="filterAll" checked>
                            <label class="form-check-label" for="filterAll">
                                全部 <span class="text-muted">({{ stats.total }})</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="categoryFilter" value="time" id="filterTime">
                            <label class="form-check-label" for="filterTime">
                                时间类型 <span class="text-muted">({{ stats.time }})</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="categoryFilter" value="power" id="filterPower">
                            <label class="form-check-label" for="filterPower">
                                功率类型 <span class="text-muted">({{ stats.power }})</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="categoryFilter" value="other" id="filterOther">
                            <label class="form-check-label" for="filterOther">
                                其他类型 <span class="text-muted">({{ stats.other }})</span>
                            </label>
                        </div>
                    </div>

                    <!-- 状态筛选 -->
                    <div class="mb-3">
                        <label class="form-label">状态筛选</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showActiveOnly" checked>
                            <label class="form-check-label" for="showActiveOnly">
                                仅显示启用的寄存器
                            </label>
                        </div>
                    </div>

                    <!-- 重置按钮 -->
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-secondary" id="resetFiltersBtn">
                            <i class="fas fa-undo me-1"></i>重置筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 寄存器列表 -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>寄存器列表
                        <span class="badge bg-secondary ms-2" id="visibleCount">{{ configs|length }}</span>
                    </h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="expandAllBtn">
                            <i class="fas fa-expand-alt me-1"></i>展开全部
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="collapseAllBtn">
                            <i class="fas fa-compress-alt me-1"></i>收起全部
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="register-grid" id="registerGrid">
                        {% for config in configs %}
                        <div class="register-card" data-category="{{ config.category }}" data-active="{{ config.is_active|lower }}">
                            <div class="register-header">
                                <div>
                                    <strong>0x{{ '%04X'|format(config.register_address) }}</strong>
                                    <span class="category-badge category-{{ config.category }} ms-2">{{ config.category }}</span>
                                </div>
                                <div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox"
                                               data-field="is_active" data-id="{{ config.id }}"
                                               {{ 'checked' if config.is_active else '' }}>
                                    </div>
                                </div>
                            </div>
                            <div class="register-body">
                                <div class="register-info">
                                    <div class="register-info-item">
                                        <label class="register-info-label">寄存器名称</label>
                                        <input type="text" class="editable-field"
                                               data-field="register_name" data-id="{{ config.id }}"
                                               value="{{ config.register_name or '' }}">
                                    </div>
                                    <div class="register-info-item">
                                        <label class="register-info-label">数据类型</label>
                                        <select class="form-select form-select-sm editable-field"
                                                data-field="data_type" data-id="{{ config.id }}">
                                            <option value="uint16" {{ 'selected' if config.data_type == 'uint16' else '' }}>uint16</option>
                                            <option value="int16" {{ 'selected' if config.data_type == 'int16' else '' }}>int16</option>
                                            <option value="uint32" {{ 'selected' if config.data_type == 'uint32' else '' }}>uint32</option>
                                            <option value="int32" {{ 'selected' if config.data_type == 'int32' else '' }}>int32</option>
                                            <option value="float" {{ 'selected' if config.data_type == 'float' else '' }}>float</option>
                                        </select>
                                    </div>
                                    <div class="register-info-item">
                                        <label class="register-info-label">单位</label>
                                        <input type="text" class="editable-field"
                                               data-field="unit" data-id="{{ config.id }}"
                                               value="{{ config.unit or '' }}">
                                    </div>
                                    <div class="register-info-item">
                                        <label class="register-info-label">缩放因子</label>
                                        <input type="number" step="0.001" class="editable-field"
                                               data-field="scale_factor" data-id="{{ config.id }}"
                                               value="{{ config.scale_factor or 1 }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="register-info-label">描述</label>
                                    <textarea class="editable-field" rows="2"
                                              data-field="description" data-id="{{ config.id }}"
                                              placeholder="寄存器功能描述">{{ config.description or '' }}</textarea>
                                </div>
                                <div class="register-actions">
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteRegister({{ config.id }})">
                                        <i class="fas fa-trash me-1"></i>删除
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                            onclick="testRegister({{ config.id }})">
                                        <i class="fas fa-vial me-1"></i>测试
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- 空状态 -->
                    <div id="emptyState" class="text-center py-5" style="display: none;">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">没有找到匹配的寄存器</h5>
                        <p class="text-muted">请尝试调整筛选条件或搜索关键词</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加寄存器模态框 -->
<div class="modal fade" id="addRegisterModal" tabindex="-1" aria-labelledby="addRegisterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRegisterModalLabel">添加新寄存器</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addRegisterForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">寄存器地址 *</label>
                                <input type="number" class="form-control" name="register_address" required min="0" max="65535">
                                <small class="form-text text-muted">十进制地址 (0-65535)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">分类 *</label>
                                <select class="form-select" name="category" required>
                                    <option value="">请选择分类</option>
                                    <option value="time">时间类型</option>
                                    <option value="power">功率类型</option>
                                    <option value="other">其他类型</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">寄存器名称</label>
                                <input type="text" class="form-control" name="register_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">数据类型</label>
                                <select class="form-select" name="data_type">
                                    <option value="uint16">uint16</option>
                                    <option value="int16">int16</option>
                                    <option value="uint32">uint32</option>
                                    <option value="int32">int32</option>
                                    <option value="float">float</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">单位</label>
                                <input type="text" class="form-control" name="unit">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">缩放因子</label>
                                <input type="number" step="0.001" class="form-control" name="scale_factor" value="1">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" name="description" rows="3" placeholder="寄存器功能描述"></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_active" checked>
                        <label class="form-check-label">启用此寄存器</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveRegisterBtn">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let changedFields = new Map(); // 存储已更改的字段

$(document).ready(function() {
    // 绑定事件
    bindEvents();

    // 初始化筛选
    applyFilters();

    // 初始化模态框
    initializeModal();
});

function bindEvents() {
    // 搜索框
    $('#searchInput').on('input', debounce(applyFilters, 300));

    // 分类筛选
    $('input[name="categoryFilter"]').change(applyFilters);

    // 状态筛选
    $('#showActiveOnly').change(applyFilters);

    // 重置筛选
    $('#resetFiltersBtn').click(resetFilters);

    // 展开/收起
    $('#expandAllBtn').click(() => toggleAllCards(true));
    $('#collapseAllBtn').click(() => toggleAllCards(false));

    // 可编辑字段
    $('.editable-field').on('change input', function() {
        const field = $(this).data('field');
        const id = $(this).data('id');
        const value = $(this).val();

        // 记录更改
        if (!changedFields.has(id)) {
            changedFields.set(id, {});
        }
        changedFields.get(id)[field] = value;

        // 标记为已更改
        $(this).addClass('border-warning');
        updateSaveButton();
    });

    // 保存所有更改
    $('#saveAllBtn').click(saveAllChanges);

    // 添加寄存器
    $('#saveRegisterBtn').click(saveNewRegister);
}

function initializeModal() {
    // 添加寄存器按钮点击事件
    $('#addRegisterBtn').click(function() {
        const modalElement = document.getElementById('addRegisterModal');
        console.log('Modal element:', modalElement); // 调试信息

        if (!modalElement) {
            console.error('找不到模态框元素 addRegisterModal');
            showAlert('模态框初始化失败', 'danger');
            return;
        }

        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    });
}

function applyFilters() {
    const searchTerm = $('#searchInput').val().toLowerCase();
    const category = $('input[name="categoryFilter"]:checked').val();
    const showActiveOnly = $('#showActiveOnly').is(':checked');

    let visibleCount = 0;

    $('.register-card').each(function() {
        const $card = $(this);
        const cardCategory = $card.data('category');
        const cardActive = $card.data('active');

        // 搜索匹配
        const cardText = $card.text().toLowerCase();
        const matchesSearch = !searchTerm || cardText.includes(searchTerm);

        // 分类匹配
        const matchesCategory = category === 'all' || cardCategory === category;

        // 状态匹配
        const matchesActive = !showActiveOnly || cardActive === true;

        const shouldShow = matchesSearch && matchesCategory && matchesActive;

        $card.toggle(shouldShow);
        if (shouldShow) visibleCount++;
    });

    $('#visibleCount').text(visibleCount);
    $('#emptyState').toggle(visibleCount === 0);
}

function resetFilters() {
    $('#searchInput').val('');
    $('#filterAll').prop('checked', true);
    $('#showActiveOnly').prop('checked', true);
    applyFilters();
}

function toggleAllCards(expand) {
    $('.register-body').toggle(expand);
}

function updateSaveButton() {
    const hasChanges = changedFields.size > 0;
    $('#saveAllBtn').prop('disabled', !hasChanges);

    if (hasChanges) {
        $('#saveAllBtn').html('<i class="fas fa-save me-1"></i>保存所有更改 (' + changedFields.size + ')');
    } else {
        $('#saveAllBtn').html('<i class="fas fa-save me-1"></i>保存所有更改');
    }
}

function saveAllChanges() {
    if (changedFields.size === 0) return;

    const updates = [];
    changedFields.forEach((fields, id) => {
        updates.push({ id: parseInt(id), ...fields });
    });

    $('#saveAllBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');

    $.ajax({
        url: '/api/register_config/batch_update',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ updates: updates }),
        success: function(response) {
            if (response.success) {
                showAlert('批量更新成功', 'success');
                changedFields.clear();
                $('.editable-field').removeClass('border-warning');
                updateSaveButton();
            } else {
                showAlert('批量更新失败: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('批量更新请求失败', 'danger');
        },
        complete: function() {
            $('#saveAllBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存所有更改');
        }
    });
}

function saveNewRegister() {
    const formData = new FormData(document.getElementById('addRegisterForm'));
    const data = Object.fromEntries(formData.entries());
    data.is_active = formData.has('is_active');

    $('#saveRegisterBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');

    $.ajax({
        url: '/api/register_config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                showAlert('寄存器添加成功', 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('addRegisterModal'));
                if (modal) {
                    modal.hide();
                }
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('添加失败: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('添加请求失败', 'danger');
        },
        complete: function() {
            $('#saveRegisterBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存');
        }
    });
}

function deleteRegister(id) {
    if (!confirm('确定要删除这个寄存器吗？此操作不可撤销。')) return;

    $.ajax({
        url: `/api/register_config/${id}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                showAlert('寄存器删除成功', 'success');
                $(`[data-id="${id}"]`).closest('.register-card').fadeOut();
            } else {
                showAlert('删除失败: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('删除请求失败', 'danger');
        }
    });
}

function testRegister(id) {
    showAlert('寄存器测试功能开发中...', 'info');
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    $('.alert').remove();
    $('body').prepend(alertHtml);

    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
