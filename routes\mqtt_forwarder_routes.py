#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MQTT消息转发管理路由模块
处理转发记录查看、配置管理等功能
"""

import os
import json
import re
from datetime import datetime, timedelta
from flask import Blueprint, render_template, jsonify, request, current_app
from flask_login import login_required

from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
mqtt_forwarder_bp = Blueprint('mqtt_forwarder', __name__)

@mqtt_forwarder_bp.route('/mqtt_forwarder/records', methods=['GET'])
@login_required
def forwarder_records():
    """转发记录页面"""
    return render_template('mqtt_forwarder_records.html')

@mqtt_forwarder_bp.route('/mqtt_forwarder/api/records', methods=['GET'])
@login_required
def api_get_records():
    """获取转发记录API"""
    try:
        # 获取查询参数
        device_id = request.args.get('device_id', '')
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))

        # 读取日志文件
        log_file = 'logs/mqtt_forwarder.log'
        records = []

        if os.path.exists(log_file):
            records = _read_forwarder_logs(log_file, device_id, limit, offset)

        return jsonify({
            'success': True,
            'records': records,
            'total': len(records)
        })

    except Exception as e:
        logger.error(f"获取转发记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mqtt_forwarder_bp.route('/mqtt_forwarder/api/config', methods=['GET'])
@login_required
def api_get_config():
    """获取转发配置API"""
    try:
        config_file = 'config/mqtt_forwarder_config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return jsonify({
                'success': True,
                'config': config
            })
        else:
            return jsonify({
                'success': False,
                'error': '配置文件不存在'
            }), 404

    except Exception as e:
        logger.error(f"获取转发配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mqtt_forwarder_bp.route('/mqtt_forwarder/api/status', methods=['GET'])
@login_required
def api_get_status():
    """获取转发服务状态API"""
    try:
        from services.mqtt_message_forwarder import mqtt_forwarder

        # 检查服务状态
        is_running = hasattr(mqtt_forwarder, '_iot_client') and mqtt_forwarder._iot_client is not None

        # 获取配置状态
        config_enabled = mqtt_forwarder.config.get('forwarder_config', {}).get('enabled', False)

        # 获取目标设备数量
        device_count = len(mqtt_forwarder.target_devices)

        # 获取转发规则数量
        rule_count = len(mqtt_forwarder.forwarding_rules)

        return jsonify({
            'success': True,
            'status': {
                'running': is_running,
                'enabled': config_enabled,
                'device_count': device_count,
                'rule_count': rule_count,
                'target_devices': list(mqtt_forwarder.target_devices)
            }
        })

    except Exception as e:
        logger.error(f"获取转发服务状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@mqtt_forwarder_bp.route('/mqtt_forwarder/api/stats', methods=['GET'])
@login_required
def api_get_stats():
    """获取转发统计信息API"""
    try:
        # 获取查询参数
        hours = int(request.args.get('hours', 24))  # 默认查询最近24小时

        # 读取日志文件并统计
        log_file = 'logs/mqtt_forwarder.log'
        stats = _calculate_forwarding_stats(log_file, hours)

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"获取转发统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def _read_forwarder_logs(log_file: str, device_id: str = '', limit: int = 100, offset: int = 0):
    """读取转发日志文件"""
    records = []

    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 倒序读取（最新的在前面）
        lines.reverse()

        count = 0
        skipped = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            try:
                # 解析JSON格式的日志
                if line.startswith('{"timestamp"'):
                    # 提取message部分的JSON
                    match = re.search(r'"message"\s*:\s*({.*?})', line)
                    if match:
                        message_json = match.group(1)
                        record = json.loads(message_json)

                        # 过滤设备ID
                        if device_id and record.get('device_id') != device_id:
                            continue

                        # 跳过offset数量的记录
                        if skipped < offset:
                            skipped += 1
                            continue

                        # 添加时间戳
                        timestamp_match = re.search(r'"timestamp": "([^"]+)"', line)
                        if timestamp_match:
                            record['timestamp'] = timestamp_match.group(1)

                        records.append(record)
                        count += 1

                        if count >= limit:
                            break

            except json.JSONDecodeError:
                continue

    except FileNotFoundError:
        pass
    except Exception as e:
        logger.error(f"读取转发日志失败: {e}")

    return records

def _calculate_forwarding_stats(log_file: str, hours: int = 24):
    """计算转发统计信息"""
    stats = {
        'total_messages': 0,
        'successful_messages': 0,
        'failed_messages': 0,
        'device_stats': {},
        'direction_stats': {
            'alicloud→emqx': 0,
            'emqx→alicloud': 0
        },
        'time_range': f'最近{hours}小时'
    }

    try:
        if not os.path.exists(log_file):
            return stats

        # 计算时间范围
        cutoff_time = datetime.now() - timedelta(hours=hours)

        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or not line.startswith('{"timestamp"'):
                    continue

                try:
                    # 提取时间戳
                    timestamp_match = re.search(r'"timestamp": "([^"]+)"', line)
                    if not timestamp_match:
                        continue

                    timestamp_str = timestamp_match.group(1)
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')

                    # 检查时间范围
                    if timestamp < cutoff_time:
                        continue

                    # 提取message部分的JSON
                    message_match = re.search(r'"message": ({.*})$', line)
                    if not message_match:
                        continue

                    record = json.loads(message_match.group(1))

                    # 统计总数
                    stats['total_messages'] += 1

                    # 统计成功/失败
                    if record.get('success', False):
                        stats['successful_messages'] += 1
                    else:
                        stats['failed_messages'] += 1

                    # 统计设备
                    device_id = record.get('device_id', 'unknown')
                    if device_id not in stats['device_stats']:
                        stats['device_stats'][device_id] = {
                            'total': 0,
                            'successful': 0,
                            'failed': 0
                        }
                    stats['device_stats'][device_id]['total'] += 1
                    if record.get('success', False):
                        stats['device_stats'][device_id]['successful'] += 1
                    else:
                        stats['device_stats'][device_id]['failed'] += 1

                    # 统计方向
                    direction = record.get('direction', '')
                    if direction in stats['direction_stats']:
                        stats['direction_stats'][direction] += 1

                except (json.JSONDecodeError, ValueError):
                    continue

    except Exception as e:
        logger.error(f"计算转发统计失败: {e}")

    return stats
