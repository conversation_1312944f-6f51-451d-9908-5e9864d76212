from flask import Blueprint
from flask_login import login_required
from models.device import Device
# from models.device_parameter import DeviceParameter  # 已合并到Device模型中
from utils.logger import setup_logging
from services.iot_client_manager import IoTClientManager
from iot_client.functions.register_manager import RegisterManager
from iot_client.bin_block.reg_addr import RegAddr
from services.device_location import update_device_location_db
from flask import render_template, jsonify, request, flash, redirect, url_for
from models.database import db
from services.async_task_manager import get_task_manager
from services.async_device_service import AsyncDeviceService

# 获取日志记录器
logger = setup_logging()

device_parameters_bp = Blueprint('device_parameters', __name__)

@device_parameters_bp.route('/device/<int:id>/parameters', methods=['GET'])
@login_required
def device_parameters(id):
    """设备参数页面"""
    try:
        device = Device.query.get_or_404(id)

        # 获取设备的所有寄存器参数
        parameters = device.get_all_register_parameters()

        # 构建参数字典，用于模板中的JavaScript
        parameter_data = {}
        for param in parameters:
            parameter_data[param['name']] = {
                'value': param['value'] if param['value'] is not None else '--',
                'address': param['address'],
                'description': param['description'] if param['description'] else '',
                'data_type': param['data_type'] if param['data_type'] else 'integer',
                'category': param.get('category', 'other'),
                'unit': param.get('unit', ''),
                'alias': param.get('alias', param['name'])
            }

        return render_template('device_parameters.html',
                             device=device,
                             parameters=parameters,
                             parameter_data=parameter_data)

    except Exception as e:
        print(f"设备参数页面错误: {e}")
        import traceback
        traceback.print_exc()
        flash(f'获取设备参数失败: {str(e)}', 'error')
        return redirect(url_for('device.devices'))






@device_parameters_bp.route('/api/device/<device_id>/location', methods=['GET'])
def get_device_location(device_id):
    """获取设备位置"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 读取所有寄存器
        result = {}

        # 读取位置信息寄存器 REG_LOCATION_CODE - REG_LOCATION_LONGITUDE_L
        def read_location_info(device_id:str):
            msg = reg_manager.read_registers(int(device_id), RegAddr.REG_LOCATION_CODE, 5)
            if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                values = msg['parsed_data']['register_value']
                if len(values) == 5:
                    location_code = values[0]
                    latitude = ((values[1] & 0xFFFF) << 16 | values[2]) * 1e-7
                    longitude = ((values[3] & 0xFFFF) << 16 | values[4]) * 1e-7
                    logger.info(f"位置信息: 位置代码: {location_code}, 纬度: {latitude}, 经度: {longitude}")
                    # 更新数据库
                    update_device_location_db(device_id, latitude, longitude, "")
                    # 返回位置信息
                    return {
                        'location_code': location_code,
                        'latitude': latitude,
                        'longitude': longitude
                    }
            return None

        # 读取位置信息
        location_info = read_location_info(device.device_id)

        if location_info:
            return jsonify({
                'success': True,
                'location': location_info
            })
        else:
            return jsonify({'error': '无法获取设备位置信息'}), 500

    except Exception as e:
        logger.error(f"获取设备位置异常: {e}")
        return jsonify({'error': str(e)}), 500


@device_parameters_bp.route('/api/device/<device_id>/current_rms_info', methods=['GET'])
def get_device_current_rms_info(device_id):
    """获取设备电流RMS信息"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 查询设备电流RMS信息，查询类型为1
        msg = reg_manager.cmd_request_debug_info_query(int(device.device_id), 1)
        if msg:
            # 检查是否有命令响应信息
            parsed_data = msg.get('parsed_data', {})
            # 提取需要的信息
            session_id = parsed_data.get('session_id', 0)
            result = parsed_data.get('result', 1)  # 默认为错误
            info = parsed_data.get('info', {})

            # 构建简化的响应
            response = {
                'session_id': session_id,
                'result': result,
                'info': info
            }
            return jsonify(response)
        else:
            return jsonify({'error': '获取设备电流RMS信息失败'}), 500
    except Exception as e:
        logger.error(f"获取设备电流RMS信息异常: {e}")
        return jsonify({'error': str(e)}), 500


@device_parameters_bp.route('/api/device/<device_id>/wireless_charger_info', methods=['GET'])
def get_device_wireless_charger_info(device_id):
    """获取设备无线充电映射表信息"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 查询设备无线充电映射表信息，查询类型为3
        msg = reg_manager.cmd_request_debug_info_query(int(device.device_id), 3)
        if msg:
            # 检查是否有命令响应信息
            parsed_data = msg.get('parsed_data', {})
            # 提取需要的信息
            session_id = parsed_data.get('session_id', 0)
            result = parsed_data.get('result', 1)  # 默认为错误
            info = parsed_data.get('info', {})

            # 构建简化的响应
            response = {
                'session_id': session_id,
                'result': result,
                'info': info
            }
            return jsonify(response)
        else:
            return jsonify({'error': '获取设备无线充电映射表信息失败'}), 500
    except Exception as e:
        logger.error(f"获取设备无线充电映射表信息异常: {e}")
        return jsonify({'error': str(e)}), 500


@device_parameters_bp.route('/api/device/<device_id>/wtc_info', methods=['GET'])
def get_device_wtc_info(device_id):
    """获取设备无线充电信息"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 查询设备无线充电信息
        msg = reg_manager.cmd_request_wtc_info_query(int(device.device_id))
        if msg:
            # 检查是否有命令响应信息
            parsed_data = msg.get('parsed_data', {})
            # 提取需要的信息
            session_id = parsed_data.get('session_id', 0)
            result = parsed_data.get('result', 1)  # 默认为错误
            info = parsed_data.get('info', {})

            # 构建简化的响应
            response = {
                'session_id': session_id,
                'result': result,
                'info': info
            }
            return jsonify(response)
        else:
            return jsonify({'error': '获取设备无线充电信息失败'}), 500
    except Exception as e:
        logger.error(f"获取设备无线充电信息异常: {e}")
        return jsonify({'error': str(e)}), 500

# 定义格式化寄存器值的函数（仅用于前端显示）
def format_register_value_for_display(reg_addr, raw_value):
    """格式化寄存器值用于前端显示"""
    try:
        value = int(raw_value) if raw_value is not None else 0

        # 特殊格式化的寄存器
        if reg_addr == RegAddr.REG_VERSION_H:
            pcb_version = (value >> 8) & 0xFF
            pcb_version = "新版BL0910" if pcb_version >= 50 else "旧版霍尔"
            fw_version = value & 0xFF
            return f"PCB:{pcb_version},FW Major:{fw_version}"
        elif reg_addr == RegAddr.REG_VERSION_L:
            minor = (value >> 8) & 0xFF
            patch = value & 0xFF
            return f"FW Minor:{minor}.{patch}"
        elif reg_addr == RegAddr.REG_CTRL1:
            # 解析控制寄存器的各个位
            sim_mode = (value >> 0) & 0x01
            led_mode = (value >> 1) & 0x01
            wtc_map_req = (value >> 2) & 0x01
            wtc_timeout_stop = (value >> 3) & 0x01

            sim_status = "SIM卡拔出无操作" if sim_mode == 1 else "SIM卡拔出启动所有口"
            led_status = "LED闪烁" if led_mode == 1 else "LED呼吸灯"

            result = f"{value} (SIM:{sim_status}, LED:{led_status}"
            if wtc_map_req:
                result += ", 无线充电编号获取:开启"
            if wtc_timeout_stop:
                result += ", 无线充电超时停止:开启"
            result += ")"
            return result
        elif reg_addr == RegAddr.REG_PERSENTAGE:
            # 百分比需要除以100
            percentage = value / 100.0
            return f"{value} ({percentage:.1f}%)"
        elif reg_addr == RegAddr.REG_CSQ:
            # CSQ信号质量特殊解析：g_pSysInfo->reg[REG_CSQ] = (uint16_t)(csq << 8) | ber;
            csq = (value >> 8) & 0xFF  # 高字节是CSQ
            ber = value & 0xFF         # 低字节是BER

            # CSQ信号质量等级判断
            if csq == 99:
                csq_desc = "未知"
            elif csq >= 20:
                csq_desc = "优秀"
            elif csq >= 15:
                csq_desc = "良好"
            elif csq >= 10:
                csq_desc = "一般"
            elif csq >= 5:
                csq_desc = "较差"
            else:
                csq_desc = "很差"

            return f"{value} (CSQ:{csq}({csq_desc}), BER:{ber})"
        elif reg_addr == RegAddr.REG_ERROR_CNT1:
            # 错误计数器1：高字节为服务器掉线次数，低字节为SIM卡被拔出的次数
            server_offline_count = (value >> 8) & 0xFF
            sim_pull_count = value & 0xFF
            return f"{value} (服务器掉线:{server_offline_count}次, SIM卡拔出:{sim_pull_count}次)"
        elif reg_addr == RegAddr.REG_ERROR_CNT2:
            # 错误计数器2：高字节为电压过零中断周期小于工频周期的次数，低字节为电压过零中断周期大于工频周期的次数
            short_period_count = (value >> 8) & 0xFF
            long_period_count = value & 0xFF
            return f"{value} (周期过短:{short_period_count}次, 周期过长:{long_period_count}次)"
        elif reg_addr == RegAddr.REG_COMPILER_TS_H or reg_addr == RegAddr.REG_COMPILER_TS_L:
            # 编译时间戳，需要组合高低位才有意义，这里只显示原值
            return f"{value} (编译时间戳{'高位' if reg_addr == RegAddr.REG_COMPILER_TS_H else '低位'})"
        elif reg_addr == RegAddr.REG_LOCATION_LATITUDE_H or reg_addr == RegAddr.REG_LOCATION_LATITUDE_L:
            # 纬度，需要组合高低位才有意义，这里只显示原值
            return f"{value} (纬度{'高位' if 'H' in RegAddr.get_reg_name(reg_addr) else '低位'})"
        elif reg_addr == RegAddr.REG_LOCATION_LONGITUDE_H or reg_addr == RegAddr.REG_LOCATION_LONGITUDE_L:
            # 经度，需要组合高低位才有意义，这里只显示原值
            return f"{value} (经度{'高位' if 'H' in RegAddr.get_reg_name(reg_addr) else '低位'})"
        else:
            # 其他寄存器直接显示整数值，不添加单位
            return str(value)
    except (ValueError, TypeError):
        return str(raw_value) if raw_value is not None else "--"

def update_device_version(device: Device, version_h_raw: int, version_l_raw: int) -> bool:
    """
    根据原始寄存器值更新设备版本号

    Args:
        device: 设备对象
        version_h_raw: REG_VERSION_H原始值
        version_l_raw: REG_VERSION_L原始值

    Returns:
        bool: 更新是否成功
    """
    try:
        # 使用统一的版本解析工具
        from utils.ota_common import VersionUtils
        new_version = VersionUtils.parse_version_from_registers(version_h_raw, version_l_raw)

        # 比较版本号，只有在版本号不同时才更新
        if not device.firmware_version or new_version != device.firmware_version:
            device.firmware_version = new_version
            db.session.commit()
            logger.info(f"设备 {device.device_id} 版本号已更新为: {new_version}")
            return True
        else:
            logger.debug(f"设备 {device.device_id} 版本号未变化: {new_version}")
            return True
    except Exception as e:
        logger.error(f"更新设备版本号异常: {e}")
    return False

def update_device_type(device: Device, version_h_raw: int) -> bool:
    """
    根据REG_VERSION_H原始值更新设备类型

    Args:
        device: 设备对象
        version_h_raw: REG_VERSION_H原始值

    Returns:
        bool: 更新是否成功
    """
    try:
        # 解析设备类型：REG_VERSION_H的高8位
        device_type = (version_h_raw >> 8) & 0xFF

        # 检查设备类型是否有效
        if device_type in [10, 50, 51]:
            # 只有当设备当前没有设置设备类型或设备类型不同时才更新
            if device.device_type != device_type:
                device.device_type = device_type
                db.session.commit()
                logger.info(f"设备 {device.device_id} 设备类型已更新为: {device_type}")
                return True
            else:
                logger.debug(f"设备 {device.device_id} 设备类型未变化: {device_type}")
                return True
        else:
            logger.warning(f"设备 {device.device_id} 设备类型无效: {device_type}")
            return False
    except Exception as e:
        logger.error(f"更新设备类型异常: {e}")
    return False

def _query_single_device_parameters(device, iot_client):
    """查询单个设备参数的内部函数"""
    # 构建topic
    topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

    # 创建寄存器管理器
    reg_manager = RegisterManager(iot_client, topic_full_name, logger)

    # 存储原始寄存器值
    raw_values = {}

    # 读取寄存器并存储原始值的函数
    def read_and_store_raw_registers(device_id, start_addr, count):
        msg = reg_manager.read_registers(int(device_id), start_addr, count)
        if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
            values = msg['parsed_data']['register_value']
            for i, value in enumerate(values):
                reg_addr = start_addr + i
                reg_name = RegAddr.get_reg_name(reg_addr)
                raw_values[reg_name] = value

    # 读取T1-T13寄存器
    read_and_store_raw_registers(device.device_id, RegAddr.REG_T1, 13)

    # 读取P4-P16寄存器
    read_and_store_raw_registers(device.device_id, RegAddr.REG_P4, 13)

    # 读取新增的无线充电相关寄存器 (REG_UID_PROTECT_KEY2到REG_WTC_CTRL，共3个寄存器)
    read_and_store_raw_registers(device.device_id, RegAddr.REG_UID_PROTECT_KEY2, 3)

    # 将原始值保存到数据库
    for reg_name, raw_value in raw_values.items():
        # 根据寄存器名称设置别名和数据类型
        alias = RegAddr.get_reg_desc(reg_name) or reg_name
        data_type = 'integer'  # 默认类型
        if reg_name == 'REG_PERSENTAGE':
            data_type = 'float'
        elif reg_name in ['REG_CSQ', 'REG_VERSION_H', 'REG_VERSION_L', 'REG_CTRL1']:
            data_type = 'hex'

        # 存储原始值到数据库
        device.add_or_update_register_parameter(
            reg_name, alias, data_type, str(raw_value), RegAddr.get_reg_desc(reg_name)
        )

    # 提交数据库更改
    db.session.commit()
    logger.info(f"设备 {device.device_id} 的参数已保存到数据库")

    # 更新设备版本号和类型
    if 'REG_VERSION_H' in raw_values and 'REG_VERSION_L' in raw_values:
        update_device_version(device, raw_values['REG_VERSION_H'], raw_values['REG_VERSION_L'])
        update_device_type(device, raw_values['REG_VERSION_H'])

    # 构建返回结果（格式化显示）
    response = {}
    for reg_name, raw_value in raw_values.items():
        reg_addr = RegAddr.get_reg_addr_by_name(reg_name)
        formatted_value = format_register_value_for_display(reg_addr, raw_value)
        response[reg_name] = {
            'value': formatted_value,
            'raw_value': raw_value,
            'description': RegAddr.get_reg_desc(reg_name)
        }

    return response


@device_parameters_bp.route('/api/device/<device_id>/parameters', methods=['GET'])
def get_device_parameters(device_id):
    """获取设备参数"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 调用内部函数查询参数
        response = _query_single_device_parameters(device, iot_client)
        return jsonify(response)

    except Exception as e:
        logger.error(f"获取设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/device/<device_id>/parameters', methods=['POST'])
def set_device_parameters(device_id):
    """设置设备参数"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        data = request.get_json()
        if not data or 'reg_addr' not in data or 'reg_value' not in data:
            return jsonify({'error': '参数不完整'}), 400

        reg_addr = int(data['reg_addr'])
        reg_value = int(data['reg_value'])

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 写入寄存器
        success = reg_manager.write_register(int(device.device_id), reg_addr, [reg_value])

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '写入寄存器失败'}), 500

    except Exception as e:
        logger.error(f"设置设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500


@device_parameters_bp.route('/api/device/<device_id>/saved_parameters', methods=['GET'])
@login_required
def get_saved_device_parameters(device_id):
    """从数据库中获取指定设备的参数（格式化显示）"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 从统一设备模型中获取设备参数
        parameters = device.get_all_register_parameters()

        # 构建返回结果
        response = {}
        for param in parameters:
            raw_value = param['value']

            # 获取寄存器地址用于格式化
            reg_addr = None
            try:
                reg_addr = RegAddr.get_reg_addr_by_name(param['name'])
            except Exception:
                pass

            # 格式化显示值 - 使用与异步查询相同的格式化函数
            if reg_addr is not None:
                try:
                    # 导入异步设备服务中的格式化函数
                    from services.async_device_service import format_register_value_for_display as async_format_func
                    formatted_value = async_format_func(reg_addr, raw_value)
                except Exception as e:
                    logger.warning(f"使用异步格式化函数失败，回退到本地函数: {e}")
                    formatted_value = format_register_value_for_display(reg_addr, raw_value)
            else:
                formatted_value = raw_value if raw_value is not None else '--'

            response[param['name']] = {
                'value': formatted_value,
                'raw_value': raw_value,
                'description': param['description'] if param['description'] else ''
            }

        return jsonify(response)

    except Exception as e:
        logger.error(f"获取设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/parameter/add/<int:device_id>', methods=['POST'])
@login_required
def add_parameter(device_id):
    """添加设备参数"""
    device = Device.query.get_or_404(device_id)

    param_name = request.form.get('param_name')
    param_value = request.form.get('param_value')

    if not param_name or not param_value:
        flash('参数名称和值不能为空', 'danger')
        return redirect(url_for('device_parameters.device_parameters', id=device_id))

    try:
        # 这里可以添加参数到数据库或设备
        # 暂时只显示成功消息
        flash('参数添加成功', 'success')
    except Exception as e:
        logger.error(f"添加参数异常: {e}")
        flash(f'添加参数失败: {str(e)}', 'danger')

    return redirect(url_for('device_parameters.device_parameters', id=device_id))

@device_parameters_bp.route('/parameter/edit/<int:id>', methods=['POST'])
@login_required
def edit_parameter(id):
    """编辑设备参数"""
    # 这里应该从数据库获取参数
    # 暂时使用模拟数据
    param_name = request.form.get('param_name')
    param_value = request.form.get('param_value')
    device_id = request.form.get('device_id', 1)  # 从表单获取device_id

    if not param_name or not param_value:
        flash('参数名称和值不能为空', 'danger')
        return redirect(url_for('device_parameters.device_parameters', id=device_id))

    try:
        # 这里可以更新参数到数据库或设备
        # 暂时只显示成功消息
        flash('参数更新成功', 'success')
    except Exception as e:
        logger.error(f"更新参数异常: {e}")
        flash(f'更新参数失败: {str(e)}', 'danger')

    return redirect(url_for('device_parameters.device_parameters', id=device_id))

@device_parameters_bp.route('/parameter/delete/<int:param_id>/<int:device_id>', methods=['GET'])
@login_required
def delete_parameter(param_id, device_id):
    """删除设备参数"""
    try:
        # 这里可以从数据库或设备中删除参数
        # 暂时只显示成功消息
        flash('参数删除成功', 'success')
    except Exception as e:
        logger.error(f"删除参数异常: {e}")
        flash(f'删除参数失败: {str(e)}', 'danger')

    return redirect(url_for('device_parameters.device_parameters', id=device_id))

@device_parameters_bp.route('/api/device/<device_id>/error_counts', methods=['GET'])
def get_error_counts(device_id):
    """获取设备错误计数"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 一次性读取4个连续的寄存器
        msg = reg_manager.read_registers(int(device.device_id), RegAddr.REG_ERROR_CNT1, 4)
        if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
            # 修正字段名称，将 parsed_data 改为 parsed_data
            if 'parsed_data' in msg:
                msg['parsed_data'] = msg.pop('parsed_data')
            values = msg['parsed_data']['register_value']
            if len(values) == 4:
                result = {
                    'REG_ERROR_CNT1': values[0],
                    'REG_ERROR_CNT2': values[1],
                    'REG_ERROR_CNT3': values[2],
                    'REG_ERROR_CNT4': values[3]
                }
                return jsonify(result)

        # 如果读取失败，返回默认值
        return jsonify({
            'REG_ERROR_CNT1': 0,
            'REG_ERROR_CNT2': 0,
            'REG_ERROR_CNT3': 0,
            'REG_ERROR_CNT4': 0
        })

    except Exception as e:
        logger.error(f"获取错误计数异常: {e}")
        return jsonify({'error': str(e)}), 500


@device_parameters_bp.route('/api/device/<device_id>/debug_info', methods=['GET'])
def get_device_debug_info(device_id):
    """获取设备调试信息"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 查询设备调试信息，返回响应消息
        msg = reg_manager.cmd_request_debug_info_query(int(device.device_id), 0)
        if msg:
            # 检查是否有命令响应信息
            parsed_data = msg.get('parsed_data', {})
            # 提取需要的信息
            session_id = parsed_data.get('session_id', 0)
            result = parsed_data.get('result', 1)  # 默认为错误
            info = parsed_data.get('info', {})

            # 构建简化的响应
            response = {
                'session_id': session_id,
                'result': result,
                'info': info
            }
            return jsonify(response)
        else:
            return jsonify({'error': '获取设备调试信息失败'}), 500
    except Exception as e:
        logger.error(f"获取设备调试信息异常: {e}")
        return jsonify({'error': str(e)}), 500

def convert_device_type(device_type_value):
    """将设备类型数值转换为对应的版本名称"""
    device_type_map = {
        10: "V2 (旧版霍尔传感器版本，黑色PCB)",
        50: "V5 (新版BL0910 10通道版本)",
        51: "V51 (新版BL0939 2通道版本)"
    }
    return device_type_map.get(device_type_value, f"未知类型 ({device_type_value})")

@device_parameters_bp.route('/api/device/<int:device_id>/firmware_info')
@login_required
def get_device_firmware_info(device_id):
    """获取设备固件信息"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 查询设备固件信息，返回响应消息
        msg = reg_manager.cmd_request_firmware_info_query(int(device.device_id))
        if msg:
            # 检查是否有命令响应信息
            parsed_data = msg.get('parsed_data', {})
            # 提取需要的信息
            session_id = parsed_data.get('session_id', 0)
            result = parsed_data.get('result', 1)  # 默认为错误
            info = parsed_data.get('info', {})

            # 转换设备类型名称
            device_type_value = info.get('device_type', 0)
            device_type_name = convert_device_type(device_type_value)
            info['device_type_name'] = device_type_name
            info['device_type_value'] = device_type_value

            # 如果查询成功且获取到有效的设备类型，更新数据库中的设备类型
            if result == 0 and device_type_value in [10, 50, 51]:
                try:
                    # 只有当设备当前没有设置设备类型或设备类型不同时才更新
                    if device.device_type != device_type_value:
                        device.device_type = device_type_value
                        db.session.commit()
                        logger.info(f"设备 {device.device_id} 的设备类型已自动更新为: {device_type_name}")
                except Exception as e:
                    logger.error(f"更新设备类型失败: {e}")
                    db.session.rollback()

            # 构建简化的响应
            response = {
                'session_id': session_id,
                'result': result,
                'info': info
            }
            return jsonify(response)
        else:
            return jsonify({'error': '获取设备固件信息失败'}), 500
    except Exception as e:
        logger.error(f"获取设备固件信息异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/devices/batch_parameters', methods=['POST'])
@login_required
def batch_set_device_parameters():
    """批量设置设备参数"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data or 'reg_addr' not in data or 'reg_value' not in data:
            return jsonify({'error': '参数不完整'}), 400

        device_ids = data['device_ids']
        reg_addr = int(data['reg_addr'])
        reg_value = int(data['reg_value'])

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                # 构建topic
                topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

                # 创建寄存器管理器，设置较短的超时时间以便快速检测超时
                reg_manager = RegisterManager(iot_client, topic_full_name, logger)

                # 写入寄存器，使用15秒超时
                logger.info(f"开始为设备 {device.device_id} 设置参数 {reg_addr}={reg_value}")
                success = reg_manager.write_register(int(device.device_id), reg_addr, [reg_value], timeout=15)

                if success:
                    logger.info(f"设备 {device.device_id} 参数设置成功")
                    results.append({
                        'device_id': device_id,
                        'success': True,
                        'error': None
                    })
                else:
                    # 写入失败，通过日志信息判断具体原因
                    error_msg = '设备超时未响应'
                    logger.warning(f"设备 {device.device_id} 参数设置失败: {error_msg}")
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': error_msg
                    })

            except Exception as e:
                error_msg = str(e)
                logger.error(f"设置设备 {device_id} 参数异常: {e}")

                # 检查是否是超时相关的异常
                if 'timeout' in error_msg.lower() or '超时' in error_msg:
                    error_msg = '设备响应超时'
                elif 'connection' in error_msg.lower() or '连接' in error_msg:
                    error_msg = '设备连接失败'

                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': error_msg
                })

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        logger.error(f"批量设置设备参数异常: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@device_parameters_bp.route('/api/devices/batch_query_parameters', methods=['POST'])
@login_required
def batch_query_device_parameters():
    """批量查询设备参数"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data:
            return jsonify({'error': '参数不完整'}), 400

        device_ids = data['device_ids']

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                # 复用单个设备查询的代码
                parameters = _query_single_device_parameters(device, iot_client)

                results.append({
                    'device_id': device_id,
                    'success': True,
                    'parameters': parameters
                })

            except Exception as e:
                logger.error(f"查询设备 {device_id} 参数异常: {e}")
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        logger.error(f"批量查询设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/devices/batch_query_locations', methods=['POST'])
@login_required
def batch_query_device_locations():
    """批量查询设备位置"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data:
            return jsonify({'error': '参数不完整'}), 400

        device_ids = data['device_ids']

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                # 构建topic
                topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

                # 创建寄存器管理器
                reg_manager = RegisterManager(iot_client, topic_full_name, logger)

                # 读取位置信息寄存器
                msg = reg_manager.read_registers(int(device.device_id), RegAddr.REG_LOCATION_CODE, 5)
                if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                    values = msg['parsed_data']['register_value']
                    if len(values) == 5:
                        location_code = values[0]
                        latitude = ((values[1] & 0xFFFF) << 16 | values[2]) * 1e-7
                        longitude = ((values[3] & 0xFFFF) << 16 | values[4]) * 1e-7

                        # 更新数据库
                        update_device_location_db(device.device_id, latitude, longitude, "")

                        results.append({
                            'device_id': device_id,
                            'success': True,
                            'location': {
                                'location_code': location_code,
                                'latitude': latitude,
                                'longitude': longitude
                            }
                        })
                    else:
                        results.append({
                            'device_id': device_id,
                            'success': False,
                            'error': '位置数据格式错误'
                        })
                else:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '无法获取位置信息'
                    })

            except Exception as e:
                logger.error(f"查询设备 {device_id} 位置异常: {e}")
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        logger.error(f"批量查询设备位置异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/device/<device_id>/sim_card_info', methods=['GET'])
def get_device_sim_card_info(device_id):
    """获取设备调试信息"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 查询设备调试信息，返回响应消息
        msg = reg_manager.cmd_request_debug_info_query(int(device.device_id), 2)
        if msg:
            # 检查是否有命令响应信息
            parsed_data = msg.get('parsed_data', {})
            info = parsed_data.get('info', {})
            # 构建简化的响应
            result = {
                    'imei_len': info.get('imei_len', 0),
                    'imei': info.get('imei', ''),
                    'iccid_len': info.get('iccid_len', 0),
                    'iccid': info.get('iccid', ''),
                    'query_type': info.get('query_type', 0)
            }
            return jsonify(result)
        else:
            return jsonify({'error': '获取设备调试信息失败'}), 500
    except Exception as e:
        logger.error(f"获取设备调试信息异常: {e}")
        return jsonify({'error': str(e)}), 500


# ==================== 异步API端点 ====================

@device_parameters_bp.route('/api/device/<device_id>/async/parameters', methods=['POST'])
@login_required
def start_async_query_parameters(device_id):
    """启动异步参数查询"""
    try:
        task_manager = get_task_manager()
        task_id = task_manager.submit_task(
            'query_parameters',
            device_id,
            AsyncDeviceService.query_device_parameters,
            device_id
        )

        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '参数查询任务已启动'
        })

    except Exception as e:
        logger.error(f"启动异步参数查询失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@device_parameters_bp.route('/api/device/<device_id>/async/error_counts', methods=['POST'])
@login_required
def start_async_query_error_counts(device_id):
    """启动异步错误计数查询"""
    try:
        task_manager = get_task_manager()
        task_id = task_manager.submit_task(
            'query_error_counts',
            device_id,
            AsyncDeviceService.query_error_counts,
            device_id
        )

        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '错误计数查询任务已启动'
        })

    except Exception as e:
        logger.error(f"启动异步错误计数查询失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@device_parameters_bp.route('/api/device/<device_id>/async/debug_info', methods=['POST'])
@login_required
def start_async_query_debug_info(device_id):
    """启动异步调试信息查询"""
    try:
        task_manager = get_task_manager()
        task_id = task_manager.submit_task(
            'query_debug_info',
            device_id,
            AsyncDeviceService.query_debug_info,
            device_id
        )

        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '调试信息查询任务已启动'
        })

    except Exception as e:
        logger.error(f"启动异步调试信息查询失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@device_parameters_bp.route('/api/device/<device_id>/async/firmware_info', methods=['POST'])
@login_required
def start_async_query_firmware_info(device_id):
    """启动异步固件信息查询"""
    try:
        task_manager = get_task_manager()
        task_id = task_manager.submit_task(
            'query_firmware_info',
            device_id,
            AsyncDeviceService.query_firmware_info,
            device_id
        )

        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '固件信息查询任务已启动'
        })

    except Exception as e:
        logger.error(f"启动异步固件信息查询失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@device_parameters_bp.route('/api/task/<task_id>/status', methods=['GET'])
@login_required
def get_task_status(task_id):
    """获取任务状态"""
    try:
        task_manager = get_task_manager()
        task_status = task_manager.get_task_status(task_id)

        if task_status:
            return jsonify({
                'success': True,
                'task': task_status
            })
        else:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404

    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@device_parameters_bp.route('/api/device/<device_id>/tasks', methods=['GET'])
@login_required
def get_device_tasks(device_id):
    """获取设备的所有任务"""
    try:
        task_manager = get_task_manager()
        task_type = request.args.get('type')  # 可选的任务类型筛选
        tasks = task_manager.get_device_tasks(device_id, task_type)

        return jsonify({
            'success': True,
            'tasks': tasks
        })

    except Exception as e:
        logger.error(f"获取设备任务失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
