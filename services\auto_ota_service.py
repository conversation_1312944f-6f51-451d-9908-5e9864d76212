#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动OTA升级服务
处理中控发送的固件升级请求，实现自动化升级功能
"""

import threading
import struct
from typing import Optional
from dataclasses import dataclass

from iot_client.bin_block.bin_block import BinBlock
from iot_client.bin_block.protocol_constants import ReqType
from services.request_handler_manager import RequestHandler
from models.device import Device
from models.latest_firmware import LatestFirmware
from models.firmware import Firmware
from models.database import db
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


@dataclass
class AutoOtaConfig:
    """自动OTA配置"""

    enabled: bool = True  # 是否启用自动升级
    force_update: bool = True  # 是否强制更新
    timeout: int = 300  # 升级超时时间（秒）
    max_retries: int = 3  # 最大重试次数
    compare_version: bool = True  # 是否比较版本号
    simulation_mode: bool = False  # 是否启用模拟观察日志模式


class AutoOtaRequestHandler(RequestHandler):
    """自动OTA升级请求处理器"""

    def __init__(self):
        self.config = AutoOtaConfig()
        self._lock = threading.Lock()
        self._app = None  # 存储Flask应用实例
        self._load_config_from_app()

    def get_supported_requests(self) -> list[int]:
        """返回支持的请求类型列表"""
        return [ReqType.B2_BMD_REQ_FIRMWARE_UPDAGRADE]

    def handle_request(self, device_id: int, session_id: int, request_msg: BinBlock) -> bool:
        """处理固件升级请求"""
        try:
            # 检查自动升级是否启用
            if not self.config.enabled:
                logger.info(f"设备 {device_id} 请求升级，但自动升级功能已禁用")
                return True  # 返回True表示已处理（虽然是忽略）

            logger.info(f"收到设备 {device_id} 的固件升级请求，会话ID: {session_id}")

            # 在单独线程中处理升级请求，避免阻塞消息处理
            threading.Thread(
                target=self._process_firmware_upgrade_request,
                args=(device_id, session_id, request_msg),
                daemon=True,
                name=f"AutoOTA-{device_id}-{session_id}",
            ).start()

            return True

        except Exception as e:
            logger.error(f"处理固件升级请求时发生错误: {e}")
            return False

    def set_app(self, app):
        """设置Flask应用实例"""
        self._app = app
        self._load_config_from_app()

    def _load_config_from_app(self):
        """从Flask应用配置加载设置"""
        try:
            if self._app:
                with self._app.app_context():
                    self.config.enabled = self._app.config.get("AUTO_OTA_ENABLED", True)
                    self.config.force_update = self._app.config.get("AUTO_OTA_FORCE_UPDATE", True)
                    self.config.timeout = self._app.config.get("AUTO_OTA_TIMEOUT", 300)
                    self.config.max_retries = self._app.config.get("AUTO_OTA_MAX_RETRIES", 3)
                    self.config.compare_version = self._app.config.get("AUTO_OTA_COMPARE_VERSION", True)
                    self.config.simulation_mode = self._app.config.get("AUTO_OTA_SIMULATION_MODE", False)
            else:
                # 尝试使用current_app
                from flask import current_app
                if current_app:
                    self.config.enabled = current_app.config.get("AUTO_OTA_ENABLED", True)
                    self.config.force_update = current_app.config.get("AUTO_OTA_FORCE_UPDATE", True)
                    self.config.timeout = current_app.config.get("AUTO_OTA_TIMEOUT", 300)
                    self.config.max_retries = current_app.config.get("AUTO_OTA_MAX_RETRIES", 3)
                    self.config.compare_version = current_app.config.get("AUTO_OTA_COMPARE_VERSION", True)
                    self.config.simulation_mode = current_app.config.get("AUTO_OTA_SIMULATION_MODE", False)
        except Exception as e:
            logger.warning(f"从应用配置加载自动OTA设置失败，使用默认值: {e}")

    def configure(self, config: AutoOtaConfig):
        """配置自动OTA服务"""
        with self._lock:
            self.config = config
        logger.info(
            f"自动OTA配置已更新: enabled={self.config.enabled}, "
            f"force_update={self.config.force_update}, "
            f"timeout={self.config.timeout}, max_retries={self.config.max_retries}"
        )

    def get_config(self) -> AutoOtaConfig:
        """获取当前配置"""
        with self._lock:
            return self.config

    def _process_firmware_upgrade_request(self, device_id: int, session_id: int, request_msg: BinBlock):
        """处理固件升级请求"""
        try:
            # 解析固件信息
            firmware_info = self._parse_firmware_info(request_msg.data)
            if not firmware_info:
                logger.warning(f"设备 {device_id} 固件信息解析失败")
                return

            device_type = firmware_info.get("device_type")
            current_fw_version = firmware_info.get("fw1_version", 0)

            logger.info(f"设备 {device_id} 信息: 类型={device_type}, 当前固件版本=0x{current_fw_version:08x}")

            # 查找设备记录并更新设备类型
            device = self._get_or_update_device(device_id, device_type)
            if not device:
                logger.warning(f"设备 {device_id} 不存在于数据库中")
                return

            # 查找设备类型对应的最新固件
            latest_firmware = LatestFirmware.get_latest_firmware_for_device_type(device_type)
            if not latest_firmware:
                logger.info(f"设备类型 {device_type} 没有配置最新固件")
                return

            # 获取固件详细信息
            firmware = Firmware.query.get(latest_firmware.id)
            if not firmware:
                logger.error(f"固件记录不存在: {latest_firmware.id}")
                return

            # 检查是否允许执行OTA（防止短时间内重复升级）
            if not self._can_perform_ota(device):
                logger.info(f"设备 {device.device_id} 最近已执行过OTA，跳过本次升级")
                return

            # 比较版本决定是否需要升级
            if self._should_upgrade(firmware_info, firmware):
                # 执行自动升级
                self._execute_auto_upgrade(device, firmware)
            else:
                logger.info(f"设备 {device.device_id} 已是最新版本，无需升级")

        except Exception as e:
            logger.error(f"处理设备 {device_id} 固件升级请求时发生错误: {e}")

    def _can_perform_ota(self, device: Device) -> bool:
        """检查是否允许执行OTA（防止短时间内重复升级）

        规则：
        1. 检查设备是否正在进行OTA（device.ota_in_progress）
        2. 检查设备最近的OTA开始时间（device.ota_start_time）
        3. 检查最近的OTA任务记录
        4. 只有距离上次OTA超过6小时才允许执行
        """
        try:
            from datetime import datetime, timedelta
            from models.ota_task import OtaTask

            # 1小时的时间间隔
            min_interval = timedelta(hours=1)
            current_time = datetime.now()

            # 检查设备OTA开始时间
            if device.ota_start_time:
                time_since_ota_start = current_time - device.ota_start_time
                if time_since_ota_start < min_interval:
                    logger.info(
                        f"设备 {device.device_id} OTA开始时间: {device.ota_start_time}, "
                        f"距离现在: {time_since_ota_start}, 小于6小时间隔"
                    )
                    return False

            # 3. 检查设备表中的最近OTA完成时间
            if device.last_ota_time:
                time_since_last_ota = current_time - device.last_ota_time
                if time_since_last_ota < min_interval:
                    logger.info(
                        f"设备 {device.device_id} 最近OTA时间: {device.last_ota_time}, "
                        f"距离现在: {time_since_last_ota}, 小于6小时间隔"
                    )
                    return False

            # 4. 检查最近的OTA任务记录
            recent_task = OtaTask.query.filter_by(device_id=device.id)\
                .order_by(OtaTask.created_at.desc()).first()

            if recent_task:
                time_since_last_task = current_time - recent_task.created_at
                if time_since_last_task < min_interval:
                    logger.info(
                        f"设备 {device.device_id} 最近OTA任务时间: {recent_task.created_at}, "
                        f"距离现在: {time_since_last_task}, 小于6小时间隔"
                    )
                    return False

            logger.info(f"设备 {device.device_id} 允许执行OTA升级")
            return True

        except Exception as e:
            logger.error(f"检查设备 {device.device_id} OTA权限时发生错误: {e}")
            # 出错时默认允许执行，避免阻塞正常升级
            return True

    def _execute_auto_upgrade(self, device: Device, firmware: Firmware):
        """执行自动升级"""
        try:
            logger.info(f"开始为设备 {device.device_id} 执行自动OTA升级，"
                       f"目标固件: {firmware.name} v{firmware.version}, "
                       f"crc32={firmware.crc32},device_type={firmware.device_type}")

            if self.config.simulation_mode:
                # 模拟模式：只记录日志，不实际执行升级
                logger.info(f"[模拟模式] 设备 {device.device_id} 自动OTA升级任务已模拟创建")
                logger.info(f"[模拟模式] 目标固件: {firmware.name} v{firmware.version}")
                logger.info(f"[模拟模式] 固件路径: {firmware.file_path}")
                logger.info("[模拟模式] 如果是实际模式，此时会调用 start_ota_task 创建升级任务")
            else:
                # 实际模式：调用实际的OTA升级服务（OTA任务会自动更新device.last_ota_time）
                from services.ota_service import start_ota_task

                # 确保在Flask应用上下文中执行
                if self._app:
                    with self._app.app_context():
                        # 标记设备开始OTA
                        self._mark_device_ota_start(device)

                        success, message = start_ota_task(
                            device_ids=[device.id],
                            firmware_id=firmware.id
                        )

                        if success:
                            logger.info(f"设备 {device.device_id} 自动OTA升级任务创建成功: {message}")
                        else:
                            logger.error(f"设备 {device.device_id} 自动OTA升级任务创建失败: {message}")
                            # 如果创建失败，清除OTA标记
                            self._mark_device_ota_end(device)
                else:
                    logger.error("Flask应用实例未设置，无法创建OTA任务")

        except Exception as e:
            logger.error(f"执行设备 {device.device_id} 自动升级时发生错误: {e}")

    def _mark_device_ota_start(self, device: Device):
        """标记设备开始OTA"""
        try:
            from datetime import datetime

            # 更新OTA开始时间
            device.ota_start_time = datetime.now()

            db.session.commit()
            logger.info(f"设备 {device.device_id} OTA状态已标记为开始")
        except Exception as e:
            logger.error(f"标记设备 {device.device_id} OTA开始状态失败: {e}")

    def _mark_device_ota_end(self, device: Device):
        """标记设备结束OTA"""
        try:
            # 清除OTA开始时间
            device.ota_start_time = None

            db.session.commit()
            logger.info(f"设备 {device.device_id} OTA状态已标记为结束")
        except Exception as e:
            logger.error(f"标记设备 {device.device_id} OTA结束状态失败: {e}")

    def _parse_firmware_info(self, data: bytes) -> Optional[dict]:
        """解析固件信息"""
        try:
            if len(data) < 64:  # 最少需要64字节
                return None

            # 解析固件信息结构
            result = data[0]
            if result != 0:
                return None

            # 解析各字段 (大端序)
            fw1_version = struct.unpack(">I", data[33:37])[0]
            device_type = struct.unpack(">H", data[45:47])[0]

            return {"result": result, "fw1_version": fw1_version, "device_type": device_type}

        except Exception as e:
            logger.error(f"解析固件信息时发生错误: {e}")
            return None

    def _get_or_update_device(self, device_id: int, device_type: int) -> Optional[Device]:
        """获取或更新设备信息"""
        try:
            # 将device_id转换为字符串，因为数据库中device_id字段是String类型
            device_id_str = str(device_id)
            device = Device.query.filter_by(device_id=device_id_str).first()
            if device:
                # 更新设备类型
                if device.device_type != device_type:
                    device.device_type = device_type
                    db.session.commit()
                    logger.info(f"设备 {device_id} 类型已更新为: {device_type}")
                return device
            else:
                logger.warning(f"设备 {device_id} 不存在于数据库中")
                return None
        except Exception as e:
            logger.error(f"获取或更新设备 {device_id} 信息时发生错误: {e}")
            return None

    def _should_upgrade(self, firmware_info: dict, firmware: Firmware) -> bool:
        """判断是否需要升级

        比较规则：
        1. 版本比较：只有当固件版本更高时才升级
        2. CRC32比较：确保固件完整性
        3. 名称比较：后期添加具体实现，目前默认允许
        """
        try:
            current_version = firmware_info.get("fw1_version", 0)
            current_device_type = firmware_info.get("device_type", 0)
            fw1_crc32 = firmware_info.get("fw1_crc32", 0)

            # 从数据库获取固件信息
            firmware_version_str = firmware.version  # 数据库中的版本字符串
            firmware_device_type = firmware.device_type  # 数据库中的设备类型
            firmware_crc32 = firmware.crc32  # 数据库中的CRC32
            firmware_name = firmware.name  # 数据库中的固件名称

            # 验证设备类型匹配
            if firmware_device_type != current_device_type:
                logger.warning(
                    f"设备类型不匹配: 当前设备类型={current_device_type}, "
                    f"固件设备类型={firmware_device_type}"
                )
                return False

            # 将版本字符串转换为数值进行比较
            firmware_version_int = self._parse_version_string(firmware_version_str)
            if firmware_version_int is None:
                logger.warning(f"无法解析固件版本: {firmware_version_str}")
                return False

            # 版本比较 - 根据配置决定是否比较版本号
            if self.config.compare_version:
                if current_version >= firmware_version_int:
                    logger.info(f"版本比较: 无需升级，当前=0x{current_version:08x}, 固件=0x{firmware_version_int:08x}")
                    return False
            else:
                logger.info("版本比较已禁用，跳过版本检查")

            # CRC32比较
            if fw1_crc32 == firmware_crc32:
                logger.warning(f"CRC32匹配: 无需升级，当前CRC32={fw1_crc32}, 固件CRC32={firmware_crc32}")
                return False

            # 名称比较 - 后期添加具体实现，目前默认允许
            logger.debug(f"固件名称: {firmware_name}")

            return True

        except Exception as e:
            logger.error(f"比较固件版本时发生错误: {e}")
            return False

    def _parse_version_string(self, version_str: str) -> Optional[int]:
        """将版本字符串转换为数值 - 使用统一工具"""
        from utils.ota_common import VersionUtils
        return VersionUtils.parse_version_string(version_str)




# 创建全局实例
auto_ota_handler = AutoOtaRequestHandler()


def initialize_auto_ota_service(app=None) -> bool:
    """初始化自动OTA升级服务"""
    try:
        from services.request_handler_manager import register_request_handler

        # 设置Flask应用实例
        if app:
            auto_ota_handler.set_app(app)

        register_request_handler(auto_ota_handler)
        logger.info("自动OTA升级服务已注册")
        return True
    except Exception as e:
        logger.error(f"初始化自动OTA升级服务失败: {e}")
        return False


# 全局实例已在上面创建，这里不需要重复创建


def configure_auto_ota_service(enabled: bool = True, force_update: bool = False,
                              timeout: int = 300, max_retries: int = 3,
                              compare_version: bool = True, simulation_mode: bool = False) -> bool:
    """配置自动OTA升级服务"""
    config = AutoOtaConfig(
        enabled=enabled,
        force_update=force_update,
        timeout=timeout,
        max_retries=max_retries,
        compare_version=compare_version,
        simulation_mode=simulation_mode
    )
    auto_ota_handler.configure(config)
    logger.info(f"自动OTA升级服务配置成功: enabled={enabled}, timeout={timeout}s, max_retries={max_retries}, "
               f"compare_version={compare_version}, simulation_mode={simulation_mode}")
    return True

def get_auto_ota_config() -> dict:
    """获取自动OTA升级服务配置"""
    config = auto_ota_handler.get_config()
    return {
        "enabled": config.enabled,
        "force_update": config.force_update,
        "timeout": config.timeout,
        "max_retries": config.max_retries,
        "compare_version": config.compare_version,
        "simulation_mode": config.simulation_mode,
    }


def get_auto_ota_status() -> dict:
    """获取自动OTA升级服务状态"""
    return {
        "service_running": True,  # 服务总是运行的
        "config": get_auto_ota_config()
    }
