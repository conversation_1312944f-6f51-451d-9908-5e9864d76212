#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时序数据API路由
提供新的简化时序数据模型的API接口
"""

from flask import Blueprint, request, jsonify, current_app
from datetime import datetime, timedelta
import json
from typing import Optional

from services.time_series_service import time_series_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
time_series_api_bp = Blueprint('time_series_api', __name__, url_prefix='/api/time_series')


@time_series_api_bp.route('/write', methods=['POST'])
def write_sensor_data():
    """写入传感器数据API"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        device_id = data.get('device_id')
        if not device_id:
            return jsonify({'error': '设备ID不能为空'}), 400

        # 提取数据字段
        bl0910_error_count = data.get('bl0910_error_count')
        bl0910_rms_values = data.get('bl0910_rms_values')
        relay_state = data.get('relay_state')
        short_period_error_count = data.get('short_period_error_count')
        long_period_error_count = data.get('long_period_error_count')
        last_zero_cross_time = data.get('last_zero_cross_time')
        voltage = data.get('voltage')
        temperature = data.get('temperature')
        total_power = data.get('total_power')
        csq = data.get('csq')
        ber = data.get('ber')
        relay_pull_fault = data.get('relay_pull_fault')
        relay_open_fault = data.get('relay_open_fault')

        # 写入数据
        success = time_series_service.write_sensor_data(
            device_id=device_id,
            bl0910_error_count=bl0910_error_count,
            bl0910_rms_values=bl0910_rms_values,
            relay_state=relay_state,
            short_period_error_count=short_period_error_count,
            long_period_error_count=long_period_error_count,
            last_zero_cross_time=last_zero_cross_time,
            voltage=voltage,
            temperature=temperature,
            total_power=total_power,
            csq=csq,
            ber=ber,
            relay_pull_fault=relay_pull_fault,
            relay_open_fault=relay_open_fault
        )

        if success:
            return jsonify({
                'success': True,
                'message': '数据写入成功',
                'device_id': device_id,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': '数据写入失败'}), 500

    except Exception as e:
        logger.error(f"写入传感器数据API异常: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


@time_series_api_bp.route('/write_hardware', methods=['POST'])
def write_hardware_data():
    """写入硬件数据包API"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        device_id = data.get('device_id')
        hardware_data_hex = data.get('hardware_data')

        if not device_id:
            return jsonify({'error': '设备ID不能为空'}), 400

        if not hardware_data_hex:
            return jsonify({'error': '硬件数据不能为空'}), 400

        # 将十六进制字符串转换为字节数组
        try:
            hardware_data = bytes.fromhex(hardware_data_hex)
        except ValueError:
            return jsonify({'error': '硬件数据格式无效，应为十六进制字符串'}), 400

        # 写入硬件数据
        success = time_series_service.write_hardware_info_data(device_id, hardware_data)

        if success:
            return jsonify({
                'success': True,
                'message': '硬件数据写入成功',
                'device_id': device_id,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': '硬件数据写入失败'}), 500

    except Exception as e:
        logger.error(f"写入硬件数据API异常: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


@time_series_api_bp.route('/voltage', methods=['GET'])
def query_voltage_data():
    """查询电压数据API"""
    try:
        device_id = request.args.get('device_id')
        start_time_str = request.args.get('start_time')
        end_time_str = request.args.get('end_time')

        if not device_id:
            return jsonify({'error': '设备ID不能为空'}), 400

        # 解析时间参数
        try:
            if start_time_str:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            else:
                start_time = datetime.now() - timedelta(hours=1)

            if end_time_str:
                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            else:
                end_time = datetime.now()
        except ValueError as e:
            return jsonify({'error': f'时间格式无效: {str(e)}'}), 400

        # 查询数据
        data = time_series_service.query_voltage_data(device_id, start_time, end_time)

        return jsonify({
            'success': True,
            'device_id': device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'data': data
        })

    except Exception as e:
        logger.error(f"查询电压数据API异常: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


@time_series_api_bp.route('/temperature', methods=['GET'])
def query_temperature_data():
    """查询温度数据API"""
    try:
        device_id = request.args.get('device_id')
        start_time_str = request.args.get('start_time')
        end_time_str = request.args.get('end_time')

        if not device_id:
            return jsonify({'error': '设备ID不能为空'}), 400

        # 解析时间参数
        try:
            if start_time_str:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            else:
                start_time = datetime.now() - timedelta(hours=1)

            if end_time_str:
                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            else:
                end_time = datetime.now()
        except ValueError as e:
            return jsonify({'error': f'时间格式无效: {str(e)}'}), 400

        # 查询数据
        data = time_series_service.query_temperature_data(device_id, start_time, end_time)

        return jsonify({
            'success': True,
            'device_id': device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'data': data
        })

    except Exception as e:
        logger.error(f"查询温度数据API异常: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


@time_series_api_bp.route('/power', methods=['GET'])
def query_power_data():
    """查询功率数据API"""
    try:
        device_id = request.args.get('device_id')
        start_time_str = request.args.get('start_time')
        end_time_str = request.args.get('end_time')

        if not device_id:
            return jsonify({'error': '设备ID不能为空'}), 400

        # 解析时间参数
        try:
            if start_time_str:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            else:
                start_time = datetime.now() - timedelta(hours=1)

            if end_time_str:
                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            else:
                end_time = datetime.now()
        except ValueError as e:
            return jsonify({'error': f'时间格式无效: {str(e)}'}), 400

        # 查询数据
        data = time_series_service.query_power_data(device_id, start_time, end_time)

        return jsonify({
            'success': True,
            'device_id': device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'data': data
        })

    except Exception as e:
        logger.error(f"查询功率数据API异常: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


@time_series_api_bp.route('/relay', methods=['GET'])
def query_relay_data():
    """查询继电器数据API"""
    try:
        device_id = request.args.get('device_id')
        start_time_str = request.args.get('start_time')
        end_time_str = request.args.get('end_time')

        if not device_id:
            return jsonify({'error': '设备ID不能为空'}), 400

        # 解析时间参数
        try:
            if start_time_str:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            else:
                start_time = datetime.now() - timedelta(hours=1)

            if end_time_str:
                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            else:
                end_time = datetime.now()
        except ValueError as e:
            return jsonify({'error': f'时间格式无效: {str(e)}'}), 400

        # 查询继电器数据（这里需要实现一个新的查询方法）
        # 暂时返回模拟数据
        data = []

        return jsonify({
            'success': True,
            'device_id': device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'data': data
        })

    except Exception as e:
        logger.error(f"查询继电器数据API异常: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


@time_series_api_bp.route('/stats', methods=['GET'])
def get_data_statistics():
    """获取数据统计API"""
    try:
        device_id = request.args.get('device_id')

        if not device_id:
            return jsonify({'error': '设备ID不能为空'}), 400

        # 获取统计数据
        stats = time_series_service.get_data_statistics(device_id)

        return jsonify({
            'success': True,
            'device_id': device_id,
            'statistics': stats
        })

    except Exception as e:
        logger.error(f"获取数据统计API异常: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


@time_series_api_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查API"""
    try:
        return jsonify({
            'success': True,
            'message': '时序数据API服务正常',
            'timestamp': datetime.now().isoformat(),
            'version': '2.0.0'
        })

    except Exception as e:
        logger.error(f"健康检查API异常: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


# 错误处理
@time_series_api_bp.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'API端点不存在'}), 404


@time_series_api_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({'error': '请求方法不被允许'}), 405


@time_series_api_bp.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500
