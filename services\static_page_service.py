#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
静态页面生成服务
用于生成静态HTML页面，提高页面加载速度
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from flask import render_template_string, current_app
from jinja2 import Environment, FileSystemLoader

from models.device import Device
from models.firmware import Firmware
from models.database import db
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class StaticPageService:
    """静态页面生成服务"""
    
    def __init__(self):
        self.static_dir = Path("static/generated")
        self.static_dir.mkdir(exist_ok=True)
        self.template_dir = Path("templates")
        
    def generate_devices_static_page(self) -> bool:
        """生成静态设备页面"""
        try:
            from flask import render_template
            from app_factory import create_app

            # 创建应用上下文
            app = create_app()
            with app.app_context():
                # 生成时间戳
                generated_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 直接使用现有的设备页面模板，添加静态标识
                html_content = render_template('devices.html',
                                             is_static=True,
                                             generated_at=generated_at)

                # 保存静态文件
                static_file = self.static_dir / "devices.html"
                with open(static_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                logger.info(f"静态设备页面生成成功: {static_file}")
                return True

        except Exception as e:
            logger.error(f"生成静态设备页面失败: {e}")
            return False
    

    

    
    def get_static_file_path(self, page_name: str) -> Optional[Path]:
        """获取静态文件路径"""
        static_file = self.static_dir / f"{page_name}.html"
        if static_file.exists():
            return static_file
        return None
    
    def is_static_file_fresh(self, page_name: str, max_age_minutes: int = 30) -> bool:
        """检查静态文件是否新鲜"""
        static_file = self.static_dir / f"{page_name}.html"
        if not static_file.exists():
            return False
        
        file_time = datetime.fromtimestamp(static_file.stat().st_mtime)
        age_minutes = (datetime.now() - file_time).total_seconds() / 60
        
        return age_minutes < max_age_minutes


# 创建全局实例
static_page_service = StaticPageService()
