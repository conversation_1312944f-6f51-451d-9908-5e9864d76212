#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备状态管理服务
处理设备状态查询和缓存
"""

import threading
import time
from datetime import datetime
from threading import Thread, Event

from flask import current_app
from models.device import Device
from services.iot_client_manager import IoTClientManager
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 设备状态查询间隔选项
DEVICE_STATUS_INTERVALS = {"10s": 10, "1m": 60, "30m": 1800, "2h": 7200}


# 设备状态查询线程
class DeviceStatusThread:
    def __init__(self, app=None):
        self.thread = None
        self.thread_stop_event = Event()
        self.interval = DEVICE_STATUS_INTERVALS["2h"]  # 默认2h
        self.app = app

    def start_thread(self):
        if not self.thread:
            self.thread = Thread(target=self.update_device_status)
            self.thread.daemon = True
            self.thread.start()
            logger.info("设备状态查询线程已启动")
        else:
            logger.info("设备状态查询线程已经在运行中")

    def stop_thread(self):
        if self.thread:
            self.thread_stop_event.set()
            self.thread = None
            logger.info("设备状态查询线程已停止")
        else:
            logger.info("设备状态查询线程未在运行中")

    def set_interval(self, interval_str):
        """设置状态查询间隔

        Args:
            interval_str: 时间间隔字符串，如'10s', '1m', '30m', '2h'

        Returns:
            bool: 设置是否成功
        """
        if interval_str in DEVICE_STATUS_INTERVALS:
            self.interval = DEVICE_STATUS_INTERVALS[interval_str]
            logger.info(f"设备状态查询间隔已设置为 {interval_str} ({self.interval}秒)")
            return True
        else:
            logger.error(f"无效的时间间隔: {interval_str}")
            return False

    def update_device_status(self):
        while not self.thread_stop_event.is_set():
            with self.app.app_context():
                try:
                    if IoTClientManager.is_running():
                        logger.info("开始批量查询设备状态...")
                        iot_client = IoTClientManager.get_instance()
                        devices = Device.query.all()

                        # 按产品分组设备，以便批量查询
                        product_devices = {}
                        for device in devices:
                            product_key = device.product_key
                            if product_key not in product_devices:
                                product_devices[product_key] = []
                            product_devices[product_key].append(device)

                        # 对每个产品批量查询设备状态
                        for product_key, product_device_list in product_devices.items():
                            try:
                                # 每次最多查询50个设备（API限制）
                                for i in range(0, len(product_device_list), 50):
                                    batch_devices = product_device_list[i : i + 50]

                                    # 准备设备列表 - 使用列表推导式
                                    device_list = [(device.device_id, device.product_key) for device in batch_devices]

                                    # 批量获取设备状态
                                    if device_list:
                                        # dict[int, int]: {device_id: 1 在线 / 0 离线}
                                        batch_result: dict[int, int] = iot_client.batch_get_device_state(device_list)
                                        # 处理返回结果
                                        if len(batch_result) > 0:
                                            # 更新设备状态缓存
                                            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                            device_status_cache = current_app.config.get("DEVICE_STATUS_CACHE", {})
                                            device_status_lock = current_app.config.get("DEVICE_STATUS_LOCK")

                                            with device_status_lock:
                                                current_timestamp = int(datetime.now().timestamp())
                                                for devid, status in batch_result.items():
                                                    device_name = str(devid)
                                                    # 查找对应的设备ID
                                                    for device in batch_devices:
                                                        if device.device_id == device_name:
                                                            is_online = status == 1
                                                            device_status_cache[device.id] = {
                                                                "is_online": is_online,
                                                                "last_check": current_timestamp,
                                                            }
                                                            # 如果设备在线，更新最后在线时间
                                                            if is_online:
                                                                device_status_cache[device.id]["last_online"] = current_timestamp

                                                            # 记录设备状态日志
                                                            status_str = "在线" if is_online else "离线"
                                                            logger.info(f"设备 {device.device_id} 状态: {status_str}")
                                                            break

                                            # 设备状态更新后，失效相关缓存
                                            try:
                                                from services.cache_service import cache_service, CacheKeyManager
                                                cache_service.delete(CacheKeyManager.DEVICE_STATS)
                                                logger.debug("设备状态更新，已失效设备统计缓存")
                                            except ImportError:
                                                logger.debug("缓存服务未可用，跳过缓存失效")
                                        else:
                                            error_msg = batch_result.get("body", {}).get("ErrorMessage", "未知错误")
                                            logger.error(f"批量获取设备状态失败: {error_msg}")
                            except Exception as e:
                                logger.error(f"批量获取产品 {product_key} 下设备状态失败: {e}")
                                continue

                except Exception as e:
                    logger.error(f"更新设备状态时发生错误: {e}")

            # 等待下一次更新
            logger.info(f"等待 {self.interval} 秒后进行下一次状态查询...")
            self.thread_stop_event.wait(self.interval)


# 创建设备状态服务实例
def init_device_status_service(app):
    """初始化设备状态服务"""
    # 创建设备状态缓存和锁
    app.config["DEVICE_STATUS_CACHE"] = {}
    app.config["DEVICE_STATUS_LOCK"] = threading.Lock()

    # 创建设备状态查询线程
    app.config["DEVICE_STATUS_THREAD"] = DeviceStatusThread(app)

    logger.info("设备状态服务已初始化")
