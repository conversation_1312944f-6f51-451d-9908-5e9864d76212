{% extends "base.html" %}

{% block title %}工具箱{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <h2><i class="fas fa-toolbox"></i> 工具箱</h2>
    
    <div class="row mt-4">
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-terminal"></i> Web串口调试工具</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">一个基于Web的串口调试工具，支持串口通信、数据发送和接收。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="https://tool-box.net/emb/web-serial-debug" class="btn btn-primary" target="_blank">
                                <i class="fas fa-external-link-alt"></i> 打开工具
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-code"></i> 充电桩协议解析器</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">用于解析充电桩通信协议的工具，支持多种协议格式的解析和验证。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="{{ url_for('static', filename='tools/message-parser/index.html') }}" class="btn btn-success" target="_blank">
                                <i class="fas fa-external-link-alt"></i> 打开工具
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-line"></i> 统计计算器</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">在线描点、趋势拟合、估计预测，支持多种统计计算和数据分析功能。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="https://zizhujy.js.org/zh-CN/Plotter.html" class="btn btn-info" target="_blank">
                                <i class="fas fa-external-link-alt"></i> 打开工具
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-function"></i> 多项式曲线拟合</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">在线多项式曲线拟合工具，支持多种函数拟合和可视化分析。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="https://demo2.yunser.com/math/fitted_curve/" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt"></i> 打开工具
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-cloud"></i> 阿里云物联网平台</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">阿里云物联网设备管理平台，支持设备接入、监控和管理。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="https://iot.console.aliyun.com/devices/" class="btn btn-danger" target="_blank">
                                <i class="fas fa-external-link-alt"></i> 打开工具
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-memory"></i> Keil Microlib堆内存分析工具</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">专业的Keil Microlib堆内存可视化分析工具，支持多堆对比、内存布局分析和碎片化检测。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="{{ url_for('tools.heap_visualizer') }}" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt"></i> 打开工具
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-puzzle-piece"></i> 堆内存碎片分析工具 V2</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">专业的内存碎片可视化分析工具，支持比例模式、统一模式和深浅模式，专门优化小碎片显示。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="{{ url_for('tools.heap_visualizer_v2') }}" class="btn btn-info" target="_blank">
                                <i class="fas fa-external-link-alt"></i> 打开工具
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 