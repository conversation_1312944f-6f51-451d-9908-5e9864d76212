import os
from datetime import datetime
from models.database import db
from models.paid_download import DownloadOrder
from utils.logger import setup_logging
from services.alipay_service import AlipayService
import logging
from flask import current_app

logger = logging.getLogger(__name__)

class PaymentService:
    """支付服务类"""

    def __init__(self):
        """初始化支付服务"""
        self.alipay_service = AlipayService()

    def create_order(self, user_id, download_id, amount):
        """创建支付订单

        Args:
            user_id: 用户ID
            download_id: 下载文件ID
            amount: 支付金额

        Returns:
            dict: 包含订单信息的响应
        """
        try:
            # 检查是否已购买
            existing_order = DownloadOrder.query.filter_by(
                user_id=user_id,
                download_id=download_id,
                status='paid'
            ).first()

            if existing_order:
                return {
                    'success': False,
                    'message': '您已购买过此文件'
                }

            # 创建新订单
            order = DownloadOrder(
                user_id=user_id,
                download_id=download_id,
                amount=amount,
                status='pending',
                create_time=datetime.now()
            )

            db.session.add(order)
            db.session.commit()

            return {
                'success': True,
                'order_id': order.id,
                'order_no': order.order_no
            }

        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            db.session.rollback()
            return {
                'success': False,
                'message': '创建订单失败'
            }

    def process_payment(self, order_id):
        """处理支付

        Args:
            order_id: 订单ID

        Returns:
            dict: 包含支付信息的响应
        """
        try:
            # 获取订单信息
            order = DownloadOrder.query.get(order_id)
            if not order:
                return {
                    'success': False,
                    'message': '订单不存在'
                }

            if order.status == 'paid':
                return {
                    'success': False,
                    'message': '订单已支付'
                }

            # 创建支付宝支付
            result = self.alipay_service.create_payment(
                order_no=order.order_no,
                amount=order.amount,
                subject=f"下载文件：{order.paid_download.name}"
            )

            if not result['success']:
                return result

            return {
                'success': True,
                'pay_url': result['pay_url']
            }

        except Exception as e:
            logger.error(f"处理支付失败: {str(e)}")
            return {
                'success': False,
                'message': '处理支付失败'
            }

    def handle_payment_notify(self, data):
        """处理支付回调

        Args:
            data: 支付回调数据

        Returns:
            bool: 处理是否成功
        """
        try:
            # 验证支付回调
            if not self.alipay_service.verify_payment(data):
                return False

            # 获取订单号
            order_no = data.get('out_trade_no')
            if not order_no:
                return False

            # 更新订单状态
            order = DownloadOrder.query.filter_by(order_no=order_no).first()
            if not order or order.status != 'pending':
                return False

            order.status = 'paid'
            order.payment_time = datetime.now()
            order.payment_method = 'alipay'

            db.session.commit()
            return True

        except Exception as e:
            logger.error(f"处理支付回调失败: {str(e)}")
            db.session.rollback()
            return False

    @staticmethod
    def create_wechat_order(order):
        """创建微信支付订单"""
        try:
            # TODO: 实现微信支付
            # 这里需要集成微信支付SDK
            # 返回支付链接或二维码
            return {
                'success': True,
                'payment_url': 'https://example.com/wechat',
                'qr_code': 'base64_encoded_qr_code'
            }
        except Exception as e:
            logger.error(f"创建微信支付订单失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    @staticmethod
    def simulate_payment(order):
        """模拟支付（用于测试）"""
        try:
            order.status = 'paid'
            order.payment_time = datetime.now()
            order.payment_method = 'simulated'
            db.session.commit()
            return {'success': True, 'message': '支付成功'}
        except Exception as e:
            db.session.rollback()
            logger.error(f"模拟支付失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    @staticmethod
    def process_payment(order, payment_method):
        """处理支付"""
        if payment_method == 'alipay':
            return PaymentService.create_alipay_order(order)
        elif payment_method == 'wechat':
            return PaymentService.create_wechat_order(order)
        elif payment_method == 'simulated':
            return PaymentService.simulate_payment(order)
        else:
            return {'success': False, 'message': '不支持的支付方式'}
