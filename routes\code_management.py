#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码管理路由
用于处理代码拉取和应用重启
"""

from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from services.code_management_service import code_management_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
code_management_bp = Blueprint('code_management', __name__, url_prefix='/code_management')

@code_management_bp.route('/')
@login_required
def code_management_page():
    """代码管理页面"""
    try:
        # 获取Git状态信息
        git_status = code_management_service.get_git_status()

        return render_template('code_management.html',
                             git_status=git_status,
                             page_title='代码管理')
    except Exception as e:
        logger.error(f"加载代码管理页面失败: {str(e)}")
        return render_template('error.html',
                             error_message=f'加载代码管理页面失败: {str(e)}'), 500

@code_management_bp.route('/api/git_status')
@login_required
def api_git_status():
    """获取Git状态API"""
    try:
        status = code_management_service.get_git_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"获取Git状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取Git状态失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/pull_code', methods=['POST'])
@login_required
def api_pull_code():
    """拉取最新代码API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求拉取最新代码")

        result = code_management_service.pull_latest_code()

        if result['success']:
            logger.info(f"用户 {current_user.username} 代码拉取成功")
        else:
            logger.warning(f"用户 {current_user.username} 代码拉取失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"拉取代码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'拉取代码失败: {str(e)}'
        }), 500



@code_management_bp.route('/api/git_branch_graph')
@login_required
def api_git_branch_graph():
    """获取Git分支图API"""
    try:
        graph_data = code_management_service.get_git_branch_graph()
        return jsonify(graph_data)
    except Exception as e:
        logger.error(f"获取Git分支图失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取Git分支图失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/restart_app', methods=['POST'])
@login_required
def api_restart_app():
    """后台重启应用程序API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求后台重启应用程序")

        result = code_management_service.restart_application_background()

        if result['success']:
            logger.info(f"用户 {current_user.username} 后台重启成功")
        else:
            logger.warning(f"用户 {current_user.username} 后台重启失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"后台重启失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'后台重启失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/update_and_restart', methods=['POST'])
@login_required
def api_update_and_restart():
    """更新代码并后台重启API"""
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求更新代码并后台重启")

        result = code_management_service.update_and_restart()

        if result['success']:
            logger.info(f"用户 {current_user.username} 更新并后台重启成功")
        else:
            logger.warning(f"用户 {current_user.username} 更新并后台重启失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"更新代码并后台重启失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'更新代码并后台重启失败: {str(e)}'
        }), 500

@code_management_bp.route('/api/commit_changes', methods=['POST'])
@login_required
def api_commit_changes():
    """提交Git更改API"""
    try:
        # 获取提交消息
        data = request.get_json() or {}
        message = data.get('message', '').strip()

        if not message:
            return jsonify({
                'success': False,
                'error': '请提供提交消息'
            }), 400

        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求提交更改: {message}")

        result = code_management_service.commit_changes(message)

        if result['success']:
            logger.info(f"用户 {current_user.username} Git提交成功")
        else:
            logger.warning(f"用户 {current_user.username} Git提交失败: {result.get('error', '未知错误')}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"Git提交失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Git提交失败: {str(e)}'
        }), 500
