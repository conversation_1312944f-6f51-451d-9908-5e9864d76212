#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库会话管理器
提供线程安全的数据库会话管理功能
"""

import threading
from contextlib import contextmanager
from typing import Optional
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.exc import SQLAlchemyError
from models.database import db
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class DatabaseSessionManager:
    """数据库会话管理器 - 线程安全"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialize()
            return cls._instance

    def _initialize(self):
        """初始化会话管理器"""
        self.session_factory = None
        self.scoped_session_factory = None
        self._initialized = False

    def initialize_with_app(self, app):
        """使用Flask应用初始化会话工厂"""
        if self._initialized:
            return

        with app.app_context():
            # 创建会话工厂
            self.session_factory = sessionmaker(bind=db.engine)
            # 创建线程安全的作用域会话
            self.scoped_session_factory = scoped_session(self.session_factory)
            self._initialized = True
            logger.info("数据库会话管理器初始化完成")

    def get_session(self):
        """获取线程安全的数据库会话"""
        if not self._initialized:
            raise RuntimeError("数据库会话管理器未初始化")
        return self.scoped_session_factory()

    @contextmanager
    def get_session_context(self):
        """获取数据库会话上下文管理器"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        except Exception as e:
            session.rollback()
            logger.error(f"会话操作失败: {e}")
            raise
        finally:
            session.close()

    def remove_session(self):
        """移除当前线程的会话"""
        if self.scoped_session_factory:
            self.scoped_session_factory.remove()

    def close_all_sessions(self):
        """关闭所有会话"""
        if self.scoped_session_factory:
            self.scoped_session_factory.remove()
            logger.info("所有数据库会话已关闭")


# 创建全局实例
db_session_manager = DatabaseSessionManager()


class ThreadSafeDBOperations:
    """线程安全的数据库操作类"""

    def __init__(self, session_manager: DatabaseSessionManager):
        self.session_manager = session_manager

    def update_task_status(self, task_id: int, status: str, progress: int = None,
                          error_message: str = None, detailed_status: str = None,
                          stage_info: str = None) -> bool:
        """线程安全地更新任务状态（兼容旧数据库结构）"""
        try:
            with self.session_manager.get_session_context() as session:
                from models.ota_task import OtaTask

                task = session.query(OtaTask).filter_by(id=task_id).first()
                if not task:
                    logger.error(f"任务 {task_id} 不存在")
                    return False

                # 更新基本状态
                task.status = status
                if progress is not None:
                    task.progress = progress
                if error_message is not None:
                    task.error_message = error_message

                # 安全地更新新字段（检查字段是否存在）
                try:
                    if detailed_status and hasattr(task, 'detailed_status'):
                        task.detailed_status = detailed_status
                    elif detailed_status:
                        logger.debug(f"detailed_status字段不存在，跳过更新")
                except Exception as e:
                    logger.debug(f"更新detailed_status失败: {e}")

                try:
                    if stage_info and hasattr(task, 'stage_info'):
                        task.stage_info = stage_info
                    elif stage_info:
                        logger.debug(f"stage_info字段不存在，跳过更新")
                except Exception as e:
                    logger.debug(f"更新stage_info失败: {e}")

                # 更新时间戳
                try:
                    task.updated_at = db.func.now()
                except Exception as e:
                    logger.debug(f"更新updated_at失败: {e}")

                logger.debug(f"任务 {task_id} 状态更新: {status}, 进度: {progress}%")
                return True

        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            return False

    def update_device_ota_status(self, device_id: int, status: str,
                                firmware_version: str = None) -> bool:
        """线程安全地更新设备OTA状态"""
        try:
            with self.session_manager.get_session_context() as session:
                from models.device import Device
                from datetime import datetime

                device = session.query(Device).filter_by(id=device_id).first()
                if not device:
                    logger.error(f"设备 {device_id} 不存在")
                    return False

                device.last_ota_status = status
                if status == "成功":
                    device.last_ota_time = datetime.now()
                    if firmware_version:
                        device.firmware_version = firmware_version

                logger.debug(f"设备 {device_id} OTA状态更新: {status}")
                return True

        except Exception as e:
            logger.error(f"更新设备OTA状态失败: {e}")
            return False

    def get_task_info(self, task_id: int) -> Optional[dict]:
        """线程安全地获取任务信息"""
        try:
            with self.session_manager.get_session_context() as session:
                from models.ota_task import OtaTask
                from models.device import Device

                task = session.query(OtaTask).join(Device).filter(OtaTask.id == task_id).first()
                if not task:
                    return None

                return {
                    'id': task.id,
                    'device_id': task.device_id,
                    'device_name': task.device.device_id,
                    'product_key': task.device.product_key,
                    'firmware_path': task.firmware_path,
                    'firmware_version': task.firmware_version,
                    'status': task.status,
                    'progress': task.progress,
                    'error_message': task.error_message,
                    'created_at': task.created_at,
                    'updated_at': task.updated_at
                }

        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return None


# 创建全局数据库操作实例
thread_safe_db_ops = ThreadSafeDBOperations(db_session_manager)
