import json
import os
from typing import List, Dict, Any, Optional

class VersionManager:
    """版本配置管理器"""
    
    def __init__(self, config_path: str = "config/version_config.json"):
        self.config_path = config_path
        self._ensure_config_exists()
    
    def _ensure_config_exists(self):
        """确保配置文件存在"""
        if not os.path.exists(self.config_path):
            # 创建默认配置
            default_config = {
                "hardware_versions": [
                    {"id": 1, "name": "电动自行车", "enabled": True},
                    {"id": 2, "name": "电动三轮车", "enabled": True}
                ],
                "software_versions": [
                    {"version": "0.0.0", "enabled": True, "is_default": True}
                ]
            }
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            self._save_config(default_config)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 如果文件不存在或格式错误，返回默认配置
            return {
                "hardware_versions": [
                    {"id": 1, "name": "电动自行车", "enabled": True},
                    {"id": 2, "name": "电动三轮车", "enabled": True}
                ],
                "software_versions": [
                    {"version": "0.0.0", "enabled": True, "is_default": True}
                ]
            }
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    # 硬件版本管理
    def get_hardware_versions(self) -> List[Dict[str, Any]]:
        """获取所有硬件版本"""
        config = self._load_config()
        return config.get("hardware_versions", [])
    
    def get_enabled_hardware_versions(self) -> List[Dict[str, Any]]:
        """获取启用的硬件版本"""
        return [hv for hv in self.get_hardware_versions() if hv.get("enabled", True)]
    
    def add_hardware_version(self, name: str) -> Dict[str, Any]:
        """添加新的硬件版本"""
        config = self._load_config()
        hardware_versions = config.get("hardware_versions", [])
        
        # 生成新的ID
        max_id = max([hv.get("id", 0) for hv in hardware_versions], default=0)
        new_id = max_id + 1
        
        new_version = {
            "id": new_id,
            "name": name,
            "enabled": True
        }
        
        hardware_versions.append(new_version)
        config["hardware_versions"] = hardware_versions
        self._save_config(config)
        
        return new_version
    
    def update_hardware_version(self, version_id: int, name: str = None, enabled: bool = None) -> bool:
        """更新硬件版本"""
        config = self._load_config()
        hardware_versions = config.get("hardware_versions", [])
        
        for hv in hardware_versions:
            if hv.get("id") == version_id:
                if name is not None:
                    hv["name"] = name
                if enabled is not None:
                    hv["enabled"] = enabled
                config["hardware_versions"] = hardware_versions
                self._save_config(config)
                return True
        
        return False
    
    def delete_hardware_version(self, version_id: int) -> bool:
        """删除硬件版本"""
        config = self._load_config()
        hardware_versions = config.get("hardware_versions", [])
        
        # 不允许删除内置版本（ID 1和2）
        if version_id in [1, 2]:
            return False
        
        original_length = len(hardware_versions)
        hardware_versions = [hv for hv in hardware_versions if hv.get("id") != version_id]
        
        if len(hardware_versions) < original_length:
            config["hardware_versions"] = hardware_versions
            self._save_config(config)
            return True
        
        return False
    
    def get_hardware_version_name(self, version_id: int) -> str:
        """根据ID获取硬件版本名称"""
        for hv in self.get_hardware_versions():
            if hv.get("id") == version_id:
                return hv.get("name", f"未知类型 ({version_id})")
        return f"未知类型 ({version_id})"
    
    # 软件版本管理
    def get_software_versions(self) -> List[Dict[str, Any]]:
        """获取所有软件版本"""
        config = self._load_config()
        return config.get("software_versions", [])
    
    def get_enabled_software_versions(self) -> List[Dict[str, Any]]:
        """获取启用的软件版本"""
        return [sv for sv in self.get_software_versions() if sv.get("enabled", True)]
    
    def add_software_version(self, version: str, is_default: bool = False) -> Dict[str, Any]:
        """添加新的软件版本"""
        config = self._load_config()
        software_versions = config.get("software_versions", [])
        
        # 检查版本是否已存在
        for sv in software_versions:
            if sv.get("version") == version:
                return sv
        
        # 如果设置为默认版本，取消其他版本的默认状态
        if is_default:
            for sv in software_versions:
                sv["is_default"] = False
        
        new_version = {
            "version": version,
            "enabled": True,
            "is_default": is_default
        }
        
        software_versions.append(new_version)
        config["software_versions"] = software_versions
        self._save_config(config)
        
        return new_version
    
    def update_software_version(self, version: str, enabled: bool = None, is_default: bool = None) -> bool:
        """更新软件版本"""
        config = self._load_config()
        software_versions = config.get("software_versions", [])
        
        for sv in software_versions:
            if sv.get("version") == version:
                if enabled is not None:
                    sv["enabled"] = enabled
                if is_default is not None:
                    # 如果设置为默认版本，取消其他版本的默认状态
                    if is_default:
                        for other_sv in software_versions:
                            if other_sv != sv:
                                other_sv["is_default"] = False
                    sv["is_default"] = is_default
                config["software_versions"] = software_versions
                self._save_config(config)
                return True
        
        return False
    
    def delete_software_version(self, version: str) -> bool:
        """删除软件版本"""
        config = self._load_config()
        software_versions = config.get("software_versions", [])
        
        # 不允许删除默认版本0.0.0
        if version == "0.0.0":
            return False
        
        original_length = len(software_versions)
        software_versions = [sv for sv in software_versions if sv.get("version") != version]
        
        if len(software_versions) < original_length:
            config["software_versions"] = software_versions
            self._save_config(config)
            return True
        
        return False
    
    def get_default_software_version(self) -> str:
        """获取默认软件版本"""
        for sv in self.get_software_versions():
            if sv.get("is_default", False):
                return sv.get("version", "0.0.0")
        return "0.0.0"


# 全局实例
version_manager = VersionManager()
