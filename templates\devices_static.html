{% extends "base.html" %}

{% block title %}设备管理 - 静态快览{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 静态页面提示 -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-bolt me-2"></i>
        <strong>静态快览模式</strong> - 数据生成时间: {{ generated_at }}
        <button type="button" class="btn btn-sm btn-outline-primary ms-3" onclick="location.reload()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
        <a href="{{ url_for('device.devices') }}" class="btn btn-sm btn-outline-success ms-2">
            <i class="fas fa-cogs me-1"></i>完整功能
        </a>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    
    <!-- 页面标题和统计卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <h2 class="mb-0"><i class="fas fa-microchip text-primary"></i> 设备管理 - 快览</h2>
            </div>
        </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.total_devices }}</h4>
                            <p class="mb-0">总设备数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-microchip fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ firmwares|length }}</h4>
                            <p class="mb-0">固件版本数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-download fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ devices|length }}</h4>
                            <p class="mb-0">显示设备数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">静态</h4>
                            <p class="mb-0">页面模式</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bolt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备列表 -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0"><i class="fas fa-list text-primary"></i> 设备列表</h5>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 120px;">设备ID</th>
                            <th style="width: 150px;">设备备注</th>
                            <th style="width: 100px;">设备类型</th>
                            <th style="width: 120px;">固件版本</th>
                            <th style="width: 200px;">产品密钥</th>
                            <th style="width: 120px;">创建时间</th>
                            <th style="width: 120px;">最后升级</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for device in devices %}
                        <tr>
                            <td>
                                <span class="fw-bold text-primary">{{ device.device_id }}</span>
                            </td>
                            <td>
                                <span class="text-muted">{{ device.device_remark or '无备注' }}</span>
                            </td>
                            <td>
                                {% if device.device_type == 10 %}
                                    <span class="badge bg-primary">直流充电桩</span>
                                {% elif device.device_type == 50 %}
                                    <span class="badge bg-success">交流充电桩</span>
                                {% elif device.device_type == 51 %}
                                    <span class="badge bg-info">一体式充电桩</span>
                                {% else %}
                                    <span class="badge bg-secondary">未知类型</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ device.firmware_version or '未知' }}</span>
                            </td>
                            <td>
                                <small class="text-muted font-monospace">{{ device.product_key }}</small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {% if device.created_at %}
                                        {{ device.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {% if device.last_ota_time %}
                                        {{ device.last_ota_time.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        从未升级
                                    {% endif %}
                                </small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% if devices|length == 0 %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无设备数据</h5>
                <p class="text-muted">请添加设备或检查数据库连接</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 固件信息 -->
    {% if firmwares|length > 0 %}
    <div class="card shadow-sm mt-4">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0"><i class="fas fa-download text-success"></i> 可用固件版本</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for firmware in firmwares[:12] %}
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card border">
                        <div class="card-body p-3">
                            <h6 class="card-title mb-2">{{ firmware.version }}</h6>
                            <p class="card-text small text-muted mb-1">
                                文件: {{ firmware.name or '未知' }}
                            </p>
                            <p class="card-text small text-muted mb-0">
                                上传: 
                                {% if firmware.upload_time %}
                                    {{ firmware.upload_time.strftime('%m-%d %H:%M') }}
                                {% else %}
                                    未知
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if firmwares|length > 12 %}
            <div class="text-center mt-3">
                <small class="text-muted">显示前12个固件版本，共{{ firmwares|length }}个</small>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- 页面说明 -->
    <div class="card mt-4 border-info">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="text-info mb-2"><i class="fas fa-info-circle me-2"></i>静态快览说明</h6>
                    <p class="mb-0 text-muted">
                        此页面为静态快览模式，显示最新的{{ devices|length }}个设备信息。
                        数据在页面加载时获取，不会实时更新。如需完整功能和实时数据，请点击"完整功能"按钮。
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('device.devices') }}" class="btn btn-info">
                        <i class="fas fa-cogs me-1"></i>切换到完整功能
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 静态页面不需要动态加载，禁用相关功能
console.log('静态设备页面已加载，数据生成时间: {{ generated_at }}');

// 添加一些简单的交互效果
document.addEventListener('DOMContentLoaded', function() {
    // 表格行悬停效果
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
    
    // 设备ID点击复制功能
    const deviceIds = document.querySelectorAll('.fw-bold.text-primary');
    deviceIds.forEach(id => {
        id.style.cursor = 'pointer';
        id.title = '点击复制设备ID';
        id.addEventListener('click', function() {
            navigator.clipboard.writeText(this.textContent).then(() => {
                // 简单的提示效果
                const originalText = this.textContent;
                this.textContent = '已复制!';
                setTimeout(() => {
                    this.textContent = originalText;
                }, 1000);
            });
        });
    });
});
</script>
{% endblock %}
