{% extends "base.html" %}

{% block title %}配置管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs me-2"></i>服务器产品配置管理
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="loadConfig()">
                            <i class="fas fa-sync me-1"></i>刷新配置
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="saveConfig()">
                            <i class="fas fa-save me-1"></i>保存配置
                        </button>
                        <button type="button" class="btn btn-info btn-sm" onclick="showBackupsModal()">
                            <i class="fas fa-history me-1"></i>备份管理
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 配置编辑器 -->
                    <div class="row">
                        <div class="col-12">
                            {% set editor_id = 'configEditor' %}
                            {% set label = '配置内容 (JSON格式)' %}
                            {% set height = '500px' %}
                            {% set mode = 'text' %}
                            {% set read_only = false %}
                            {% set show_validation = true %}
                            {% set validation_id = 'testValidation' %}
                            
                            <!-- JSON编辑器 -->
                            {% include 'components/json_editor.html' %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 备份管理模态框 -->
<div class="modal fade" id="backupsModal" tabindex="-1" aria-labelledby="backupsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backupsModalLabel">
                    <i class="fas fa-history me-2"></i>配置备份管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>备份文件名</th>
                                <th>创建时间</th>
                                <th>文件大小</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="backupsTableBody">
                            <!-- 备份列表将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 通知组件 -->
<div id="notification" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <!-- 通知将通过JavaScript动态添加 -->
</div>
{% endblock %}

{% block scripts %}
<script>
let currentConfig = null;

// 页面加载时自动加载配置
document.addEventListener('DOMContentLoaded', function() {
    // 等待JSON编辑器初始化完成后再加载配置
    setTimeout(() => {
        loadConfig();
    }, 200);
});

// 加载配置
async function loadConfig() {
    try {
        const response = await fetch('/api/config/server-products');
        const data = await response.json();

        if (data.success) {
            currentConfig = data.config;

            // 使用JSON编辑器组件设置内容
            const editor = window.jsonEditors['configEditor'];
            if (editor) {
                editor.setContent(data.config);
            } else {
                // 降级方案：直接设置textarea值
                const textarea = document.getElementById('configEditor');
                if (textarea && textarea.tagName === 'TEXTAREA') {
                    textarea.value = JSON.stringify(data.config, null, 2);
                }
            }

            showNotification('配置加载成功', 'success');
            validateConfig();
        } else {
            showNotification('加载配置失败: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('加载配置失败:', error);
        showNotification('加载配置失败: ' + error.message, 'error');
    }
}

// 保存配置
async function saveConfig() {
    try {
        let config;

        // 从JSON编辑器获取内容
        const editor = window.jsonEditors['configEditor'];
        if (editor) {
            try {
                config = editor.getJSON();
                if (!config) {
                    // 尝试获取文本并解析
                    const configText = editor.getText();
                    config = JSON.parse(configText);
                }
            } catch (e) {
                showNotification('JSON格式错误: ' + e.message, 'error');
                return;
            }
        } else {
            // 降级方案：从textarea获取
            const textarea = document.getElementById('configEditor');
            if (textarea && textarea.tagName === 'TEXTAREA') {
                try {
                    config = JSON.parse(textarea.value);
                } catch (e) {
                    showNotification('JSON格式错误: ' + e.message, 'error');
                    return;
                }
            } else {
                showNotification('无法获取配置内容', 'error');
                return;
            }
        }

        // 发送保存请求
        const response = await fetch('/api/config/server-products', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ config: config })
        });

        const data = await response.json();

        if (data.success) {
            currentConfig = config;
            showNotification('配置保存成功', 'success');
            validateConfig();
        } else {
            showNotification('保存配置失败: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('保存配置失败:', error);
        showNotification('保存配置失败: ' + error.message, 'error');
    }
}

// 验证配置
function validateConfig() {
    let config;
    const resultDiv = document.getElementById('validationResult');

    // 从JSON编辑器获取内容
    const editor = window.jsonEditors['configEditor'];
    if (editor) {
        try {
            config = editor.getJSON();
            if (!config) {
                const configText = editor.getText();
                config = JSON.parse(configText);
            }
        } catch (e) {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    JSON格式错误: ${e.message}
                </div>
            `;
            resultDiv.style.display = 'block';
            return;
        }
    } else {
        // 降级方案：从textarea获取
        const textarea = document.getElementById('configEditor');
        if (textarea && textarea.tagName === 'TEXTAREA') {
            try {
                config = JSON.parse(textarea.value);
            } catch (e) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        JSON格式错误: ${e.message}
                    </div>
                `;
                resultDiv.style.display = 'block';
                return;
            }
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    无法获取配置内容
                </div>
            `;
            resultDiv.style.display = 'block';
            return;
        }
    }

    // 检查必需的键
    const requiredKeys = ['server_types', 'products', 'migration_rules'];
    const missingKeys = requiredKeys.filter(key => !(key in config));

    if (missingKeys.length > 0) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                配置验证失败：缺少必需的键: ${missingKeys.join(', ')}
            </div>
        `;
        resultDiv.style.display = 'block';
        return;
    }

    // 验证通过
    resultDiv.innerHTML = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            配置验证通过
        </div>
    `;
    resultDiv.style.display = 'block';
}

// 监听配置编辑器变化（如果使用降级方案）
const configEditorElement = document.getElementById('configEditor');
if (configEditorElement && configEditorElement.tagName === 'TEXTAREA') {
    configEditorElement.addEventListener('input', function() {
        // 延迟验证，避免频繁验证
        clearTimeout(this.validateTimeout);
        this.validateTimeout = setTimeout(validateConfig, 500);
    });
}

// 显示备份管理模态框
async function showBackupsModal() {
    try {
        const response = await fetch('/api/config/backups');
        const data = await response.json();
        
        if (data.success) {
            const tbody = document.getElementById('backupsTableBody');
            tbody.innerHTML = '';
            
            data.backups.forEach(backup => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${backup.filename}</td>
                    <td>${backup.created_time}</td>
                    <td>${formatFileSize(backup.size)}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="restoreBackup('${backup.filename}')">
                            <i class="fas fa-undo me-1"></i>恢复
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            
            const modal = new bootstrap.Modal(document.getElementById('backupsModal'));
            modal.show();
        } else {
            showNotification('加载备份列表失败: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('加载备份列表失败:', error);
        showNotification('加载备份列表失败: ' + error.message, 'error');
    }
}

// 恢复备份
async function restoreBackup(filename) {
    if (!confirm('确定要恢复此备份吗？当前配置将被覆盖。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/config/restore/${filename}`, {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('配置恢复成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('backupsModal'));
            modal.hide();
            // 重新加载配置
            loadConfig();
        } else {
            showNotification('恢复配置失败: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('恢复配置失败:', error);
        showNotification('恢复配置失败: ' + error.message, 'error');
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 显示通知
function showNotification(message, type = 'info') {
    const container = document.getElementById('notification');
    const toastId = 'toast-' + Date.now();
    
    const bgClass = type === 'success' ? 'bg-success' : 
                   type === 'error' ? 'bg-danger' : 
                   type === 'warning' ? 'bg-warning' : 'bg-info';
    
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast ${bgClass} text-white`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="toast-header ${bgClass} text-white border-0">
            <i class="fas fa-info-circle me-2"></i>
            <strong class="me-auto">系统通知</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    container.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 5000
    });
    bsToast.show();
    
    // 清理已隐藏的toast
    toast.addEventListener('hidden.bs.toast', function() {
        container.removeChild(toast);
    });
}
</script>
{% endblock %}
