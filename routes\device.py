from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required
from models.device import Device
from models.ota_task import OtaTask
from models.database import db
from models.firmware import Firmware
from flask import Blueprint
from flask import jsonify, send_file
import pandas as pd
from io import BytesIO
import openpyxl
from datetime import datetime
from sqlalchemy import or_, and_
from services.server_config_service import server_config_service
from services.device_server_config import device_server_config_manager

device_bp = Blueprint("device", __name__)


@device_bp.route("/devices")
@login_required
def devices():
    """设备管理页面"""
    # 只获取固件列表，设备列表通过Ajax加载
    firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()

    # 获取设备统计信息
    total_devices = Device.query.count()
    device_status_cache = current_app.config.get("DEVICE_STATUS_CACHE", {})
    device_status_lock = current_app.config.get("DEVICE_STATUS_LOCK")

    with device_status_lock:
        # 统计在线和离线设备数量
        online_count = sum(1 for status in device_status_cache.values() if status.get("is_online", False))
        offline_count = total_devices - online_count

    # 传递空的设备列表和统计信息
    return render_template(
        "devices.html",
        devices=[],
        firmwares=firmwares,
        device_status_cache={},
        stats={"total": total_devices, "online": online_count, "offline": offline_count},
    )


@device_bp.route("/api/devices")
@login_required
def get_devices_api():
    """获取设备列表API（支持分页、搜索、筛选）"""
    try:
        # 获取分页参数
        page = request.args.get("page", 1, type=int)
        per_page = request.args.get("per_page", 20, type=int)

        # # 限制per_page的范围，防止恶意请求
        # if per_page not in [10, 20, 50, 100]:
        #     per_page = 20  # 默认值

        # # 确保page参数有效
        # if page < 1:
        #     page = 1

        # 获取搜索和筛选参数
        search = request.args.get("search", "").strip()
        status_filter = request.args.get("status", "all")
        product_key_filter = request.args.get("product_key", "").strip()
        firmware_filter = request.args.get("firmware", "").strip()
        ota_status_filter = request.args.get("ota_status", "all")

        # 新增筛选参数
        remark_filter = request.args.get("remark", "").strip()
        debug_status_filter = request.args.get("debug_status", "all")
        quick_filter = request.args.get("quick_filter", "all")
        create_date_start = request.args.get("create_date_start", "").strip()
        create_date_end = request.args.get("create_date_end", "").strip()
        online_date_start = request.args.get("online_date_start", "").strip()
        online_date_end = request.args.get("online_date_end", "").strip()

        # 构建查询
        query = Device.query

        # 搜索条件（扩展为包含设备备注）
        if search:
            query = query.filter(or_(
                Device.device_id.contains(search),
                Device.device_remark.contains(search)
            ))

        # 产品密钥筛选
        if product_key_filter:
            query = query.filter(Device.product_key.contains(product_key_filter))

        # 固件版本筛选
        if firmware_filter:
            query = query.filter(Device.firmware_version.contains(firmware_filter))

        # 设备备注筛选
        if remark_filter:
            query = query.filter(Device.device_remark.contains(remark_filter))

        # OTA状态筛选
        if ota_status_filter != "all":
            if ota_status_filter == "success":
                query = query.filter(Device.last_ota_status == "成功")
            elif ota_status_filter == "failed":
                query = query.filter(Device.last_ota_status == "失败")
            elif ota_status_filter == "none":
                query = query.filter(
                    or_(
                        Device.last_ota_status.is_(None),
                        Device.last_ota_status == "未升级",
                        Device.last_ota_status == "未知",
                    )
                )

        # 创建时间范围筛选
        if create_date_start:
            try:
                start_date = datetime.strptime(create_date_start, "%Y-%m-%d")
                query = query.filter(Device.created_at >= start_date)
            except ValueError:
                pass

        if create_date_end:
            try:
                end_date = datetime.strptime(create_date_end, "%Y-%m-%d")
                # 设置为当天的23:59:59
                end_date = end_date.replace(hour=23, minute=59, second=59)
                query = query.filter(Device.created_at <= end_date)
            except ValueError:
                pass

        # 快速筛选
        if quick_filter != "all":
            if quick_filter == "today_created":
                today = datetime.now().date()
                query = query.filter(Device.created_at >= today)
            elif quick_filter == "no_remark":
                query = query.filter(or_(Device.device_remark.is_(None), Device.device_remark == ""))
            elif quick_filter == "upgrade_failed":
                query = query.filter(Device.last_ota_status == "失败")

        # 优化状态缓存处理：预先获取缓存和锁
        device_status_cache = current_app.config.get("DEVICE_STATUS_CACHE", {})
        device_status_lock = current_app.config.get("DEVICE_STATUS_LOCK")
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 检查是否需要状态信息的筛选
        need_status_filtering = (
            status_filter != "all" or
            debug_status_filter != "all" or
            quick_filter == "long_offline" or
            online_date_start or
            online_date_end
        )

        if need_status_filtering:
            # 对于状态筛选，需要先获取所有设备，然后筛选，最后分页
            # 这是因为状态信息存储在缓存中，无法直接在SQL中筛选
            all_devices = query.all()

            # 批量初始化缺失的设备状态缓存
            missing_device_ids = []
            with device_status_lock:
                for device in all_devices:
                    if device.id not in device_status_cache:
                        missing_device_ids.append(device.id)

                # 批量添加缺失的设备状态
                if missing_device_ids:
                    current_timestamp = int(datetime.now().timestamp())
                    default_status = {
                        "is_online": False,
                        "last_check": current_timestamp,
                    }
                    for device_id in missing_device_ids:
                        device_status_cache[device_id] = default_status.copy()

            # 构建所有设备的响应数据并应用状态筛选
            all_devices_data = []
            current_timestamp = int(datetime.now().timestamp())
            for device in all_devices:
                status_info = device_status_cache.get(
                    device.id, {"is_online": False, "last_check": current_timestamp}
                )

                device_data = {
                    "id": device.id,
                    "device_id": device.device_id,
                    "device_remark": device.device_remark or "",
                    "product_key": device.product_key,
                    "device_type": device.device_type,
                    "device_type_name": device.device_type_name,
                    "firmware_version": device.firmware_version or "未知",
                    "last_ota_status": device.last_ota_status or "未升级",
                    "last_ota_time": device.last_ota_time.strftime("%Y-%m-%d %H:%M:%S")
                    if device.last_ota_time
                    else "未升级",
                    "is_online": status_info["is_online"],
                    "last_online_time": datetime.fromtimestamp(status_info.get('last_online', status_info['last_check'])).strftime('%Y-%m-%d %H:%M:%S') if status_info['is_online'] else '未知',
                    "debug_script_enabled": device.debug_script and device.debug_script.enabled
                    if hasattr(device, "debug_script") and device.debug_script
                    else False,
                }
                all_devices_data.append(device_data)

            # 应用状态相关的筛选
            filtered_devices_data = all_devices_data

            # 状态筛选
            if status_filter != "all":
                if status_filter == "online":
                    filtered_devices_data = [d for d in filtered_devices_data if d["is_online"]]
                elif status_filter == "offline":
                    filtered_devices_data = [d for d in filtered_devices_data if not d["is_online"]]

            # 调试状态筛选
            if debug_status_filter != "all":
                if debug_status_filter == "enabled":
                    filtered_devices_data = [d for d in filtered_devices_data if d["debug_script_enabled"]]
                elif debug_status_filter == "disabled":
                    filtered_devices_data = [d for d in filtered_devices_data if not d["debug_script_enabled"]]

            # 长期离线筛选（超过7天未在线）
            if quick_filter == "long_offline":
                from datetime import timedelta
                seven_days_ago = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S")
                filtered_devices_data = [d for d in filtered_devices_data if not d["is_online"] and
                              (d["last_online_time"] == "未知" or d["last_online_time"] < seven_days_ago)]

            # 在线时间范围筛选
            if online_date_start or online_date_end:
                temp_filtered = []
                for device_data in filtered_devices_data:
                    last_online = device_data["last_online_time"]
                    if last_online == "未知":
                        continue

                    try:
                        last_online_dt = datetime.strptime(last_online, "%Y-%m-%d %H:%M:%S")

                        # 检查开始时间
                        if online_date_start:
                            start_dt = datetime.strptime(online_date_start, "%Y-%m-%d")
                            if last_online_dt < start_dt:
                                continue

                        # 检查结束时间
                        if online_date_end:
                            end_dt = datetime.strptime(online_date_end, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
                            if last_online_dt > end_dt:
                                continue

                        temp_filtered.append(device_data)
                    except ValueError:
                        continue

                filtered_devices_data = temp_filtered

            # 计算分页信息
            total_filtered = len(filtered_devices_data)
            total_pages = (total_filtered + per_page - 1) // per_page if total_filtered > 0 else 1

            # 应用分页
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            devices_data = filtered_devices_data[start_idx:end_idx]

            # 构建分页信息
            pagination_info = {
                "page": page,
                "pages": total_pages,
                "per_page": per_page,
                "total": total_filtered,
                "has_prev": page > 1,
                "has_next": page < total_pages,
                "prev_num": page - 1 if page > 1 else None,
                "next_num": page + 1 if page < total_pages else None,
            }

        else:
            # 无状态筛选：直接使用数据库分页
            pagination = query.paginate(page=page, per_page=per_page, error_out=False)
            devices = pagination.items

            # 批量初始化缺失的设备状态缓存
            missing_device_ids = []
            with device_status_lock:
                for device in devices:
                    if device.id not in device_status_cache:
                        missing_device_ids.append(device.id)

                # 批量添加缺失的设备状态
                if missing_device_ids:
                    current_timestamp = int(datetime.now().timestamp())
                    default_status = {
                        "is_online": False,
                        "last_check": current_timestamp,
                    }
                    for device_id in missing_device_ids:
                        device_status_cache[device_id] = default_status.copy()

            # 构建响应数据
            devices_data = []
            current_timestamp = int(datetime.now().timestamp())
            for device in devices:
                status_info = device_status_cache.get(
                    device.id, {"is_online": False, "last_check": current_timestamp}
                )

                device_data = {
                    "id": device.id,
                    "device_id": device.device_id,
                    "device_remark": device.device_remark or "",
                    "product_key": device.product_key,
                    "device_type": device.device_type,
                    "device_type_name": device.device_type_name,
                    "firmware_version": device.firmware_version or "未知",
                    "last_ota_status": device.last_ota_status or "未升级",
                    "last_ota_time": device.last_ota_time.strftime("%Y-%m-%d %H:%M:%S")
                    if device.last_ota_time
                    else "未升级",
                    "is_online": status_info["is_online"],
                    "last_online_time": datetime.fromtimestamp(status_info.get('last_online', status_info['last_check'])).strftime('%Y-%m-%d %H:%M:%S') if status_info['is_online'] else '未知',
                    "debug_script_enabled": device.debug_script and device.debug_script.enabled
                    if hasattr(device, "debug_script") and device.debug_script
                    else False,
                }
                devices_data.append(device_data)

            # 构建分页信息
            pagination_info = {
                "page": pagination.page,
                "pages": pagination.pages,
                "per_page": pagination.per_page,
                "total": pagination.total,
                "has_prev": pagination.has_prev,
                "has_next": pagination.has_next,
                "prev_num": pagination.prev_num,
                "next_num": pagination.next_num,
            }

        return jsonify(
            {
                "success": True,
                "devices": devices_data,
                "pagination": pagination_info,
            }
        )

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@device_bp.route("/api/devices/stats")
@login_required
def get_devices_stats():
    """获取设备统计信息"""
    try:
        # 获取设备总数
        total_devices = Device.query.count()

        # 获取设备状态缓存
        device_status_cache = current_app.config.get("DEVICE_STATUS_CACHE", {})
        device_status_lock = current_app.config.get("DEVICE_STATUS_LOCK")

        with device_status_lock:
            # 统计在线和离线设备数量
            online_count = sum(1 for status in device_status_cache.values() if status.get("is_online", False))
            offline_count = total_devices - online_count

        return jsonify(
            {"success": True, "stats": {"total": total_devices, "online": online_count, "offline": offline_count}}
        )

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@device_bp.route('/api/devices/batch_info', methods=['POST'])
@login_required
def get_batch_devices_info():
    """获取批量设备的详细信息"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data:
            return jsonify({'success': False, 'error': '缺少设备ID列表'}), 400

        device_ids = data['device_ids']
        if not device_ids:
            return jsonify({'success': True, 'devices': []})

        # 查询设备信息
        devices = Device.query.filter(Device.id.in_(device_ids)).all()

        devices_info = []
        for device in devices:
            devices_info.append({
                'id': device.id,
                'device_id': device.device_id,
                'device_remark': device.device_remark or '',
                'product_key': device.product_key,
                'firmware_version': device.firmware_version or '未知',
                'device_type': device.device_type,
                'device_type_name': device.device_type_name,
                'created_at': device.created_at.strftime('%Y-%m-%d %H:%M:%S') if device.created_at else ''
            })

        return jsonify({
            'success': True,
            'devices': devices_info
        })

    except Exception as e:
        # logger.error(f"获取批量设备信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@device_bp.route("/api/server-config/products")
@login_required
def get_server_products():
    """获取服务器产品配置信息"""
    try:
        return jsonify(
            {
                "success": True,
                "server_types": server_config_service.get_server_types(),
                "products": server_config_service.get_all_products(),
            }
        )
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@device_bp.route("/api/server-config/available-targets/<product_key>")
@login_required
def get_available_targets(product_key):
    """获取指定产品可迁移的目标产品列表"""
    try:
        targets = server_config_service.get_available_targets(product_key)
        current_server_type = server_config_service.detect_server_type(product_key)

        return jsonify({"success": True, "current_server_type": current_server_type, "available_targets": targets})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@device_bp.route("/api/device/<int:device_id>/server-config", methods=["POST"])
@login_required
def update_device_server_config(device_id):
    """更新单个设备的服务器配置"""
    try:
        device = Device.query.get_or_404(device_id)
        data = request.get_json()

        source_product_key = device.product_key
        target_product_key = data.get("target_product_key", "").strip()
        new_device_id = data.get("new_device_id", "") or None
        if isinstance(new_device_id, str):
            new_device_id = new_device_id.strip()
        new_device_secret = data.get("new_device_secret", "") or None
        if isinstance(new_device_secret, str):
            new_device_secret = new_device_secret.strip()

        if not target_product_key:
            return jsonify({"success": False, "message": "目标产品密钥不能为空"}), 400

        ori_device_id = device.device_id
        # 执行配置更新
        success, message = device_server_config_manager.update_device_server_config(
            ori_device_id, source_product_key, target_product_key, new_device_id, new_device_secret
        )

        if success:
            # 重新获取设备信息（可能已被更新）
            updated_device = Device.query.get(device_id)
            if updated_device:
                return jsonify(
                    {
                        "success": True,
                        "message": message,
                        "device": {
                            "id": updated_device.id,
                            "device_id": updated_device.device_id,
                            "product_key": updated_device.product_key,
                        },
                    }
                )
            else:
                return jsonify(
                    {
                        "success": True,
                        "message": message,
                        "device": {
                            "id": device.id,
                            "device_id": new_device_id or device.device_id,
                            "product_key": target_product_key,
                        },
                    }
                )
        else:
            return jsonify({"success": False, "message": message}), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "message": f"配置更新失败：{str(e)}"}), 500


@device_bp.route("/api/devices/batch-server-config", methods=["POST"])
@login_required
def batch_update_server_config():
    """批量更新设备服务器配置"""
    try:
        data = request.get_json()
        device_ids = data.get("device_ids", [])
        source_product_key = data.get("source_product_key", "").strip()
        target_product_key = data.get("target_product_key", "").strip()

        if not device_ids:
            return jsonify({"success": False, "message": "请选择要配置的设备"}), 400

        if not source_product_key or not target_product_key:
            return jsonify({"success": False, "message": "源产品密钥和目标产品密钥不能为空"}), 400

        # 执行批量配置
        has_success, message, results = device_server_config_manager.batch_update_device_server_config(
            device_ids, source_product_key, target_product_key
        )

        if has_success:
            # 统计成功和失败数量
            success_count = sum(1 for result in results if result["success"])
            total_count = len(results)

            return jsonify(
                {
                    "success": True,
                    "message": message,
                    "results": results,
                    "summary": {"total": total_count, "success": success_count, "failed": total_count - success_count},
                }
            )
        else:
            return jsonify({"success": False, "message": message, "results": results}), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "message": f"批量配置失败：{str(e)}"}), 500


@device_bp.route("/device/add", methods=["GET", "POST"])
@login_required
def add_device():
    """添加设备"""
    if request.method == "POST":
        device_id = request.form.get("device_id")
        device_remark = request.form.get("device_remark")
        product_key = request.form.get("product_key")

        # 检查设备ID是否已存在
        if Device.query.filter_by(device_id=device_id).first():
            flash("设备ID已存在", "danger")
            return redirect(url_for("device.add_device"))

        # 创建新设备
        device = Device(device_id=device_id, device_remark=device_remark, product_key=product_key)

        db.session.add(device)
        db.session.commit()

        flash("设备添加成功", "success")
        return redirect(url_for("device.devices"))

    return render_template("device_form.html")


@device_bp.route("/api/device/<int:device_id>", methods=["GET"])
@login_required
def get_device_info(device_id):
    """获取单个设备信息API"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({"success": False, "error": "设备不存在"}), 404

        return jsonify({
            "success": True,
            "device": {
                "id": device.id,
                "device_id": device.device_id,
                "remark": device.device_remark,
                "product_key": device.product_key,
                "created_at": device.created_at.isoformat() if device.created_at else None,
                "updated_at": device.updated_at.isoformat() if device.updated_at else None
            }
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@device_bp.route("/api/device/add", methods=["POST"])
@login_required
def add_device_api():
    """Ajax添加设备API"""
    try:
        data = request.get_json()
        device_id = data.get("device_id", "").strip()
        device_remark = data.get("device_remark", "").strip()
        product_key = data.get("product_key", "").strip()
        device_type = data.get("device_type")

        if not device_id or not product_key:
            return jsonify({"success": False, "message": "设备ID和产品密钥不能为空"}), 400

        # 检查设备ID是否已存在
        if Device.query.filter_by(device_id=device_id).first():
            return jsonify({"success": False, "message": "设备ID已存在"}), 400

        # 处理设备类型
        if device_type:
            try:
                device_type = int(device_type)
                if device_type not in [10, 50, 51]:
                    return jsonify({"success": False, "message": "无效的设备类型"}), 400
            except ValueError:
                return jsonify({"success": False, "message": "设备类型格式错误"}), 400
        else:
            device_type = None

        # 创建新设备
        device = Device(
            device_id=device_id,
            device_remark=device_remark,
            product_key=product_key,
            device_type=device_type
        )

        db.session.add(device)
        db.session.commit()

        return jsonify(
            {
                "success": True,
                "message": "设备添加成功",
                "device": {
                    "id": device.id,
                    "device_id": device.device_id,
                    "device_remark": device.device_remark,
                    "product_key": device.product_key,
                    "device_type": device.device_type,
                    "device_type_name": device.device_type_name,
                },
            }
        )

    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "message": f"添加设备失败：{str(e)}"}), 500


@device_bp.route("/api/device/batch-add", methods=["POST"])
@login_required
def batch_add_device_api():
    """批量添加设备API"""
    try:
        data = request.get_json()
        device_ids = data.get("device_ids", [])
        device_remark = data.get("device_remark", "").strip()
        product_key = data.get("product_key", "").strip()
        device_type = data.get("device_type")

        if not device_ids or not product_key:
            return jsonify({"success": False, "message": "设备ID列表和产品密钥不能为空"}), 400

        # 处理设备类型
        if device_type:
            try:
                device_type = int(device_type)
                if device_type not in [10, 50, 51]:
                    return jsonify({"success": False, "message": "无效的设备类型"}), 400
            except ValueError:
                return jsonify({"success": False, "message": "设备类型格式错误"}), 400
        else:
            device_type = None

        results = []
        success_count = 0
        failed_count = 0

        # 处理每个设备ID
        for device_id in device_ids:
            device_id = device_id.strip()
            if not device_id:
                continue

            try:
                # 检查设备ID是否已存在
                if Device.query.filter_by(device_id=device_id).first():
                    results.append({
                        "device_id": device_id,
                        "status": "failed",
                        "error": "设备ID已存在"
                    })
                    failed_count += 1
                    continue

                # 创建新设备
                device = Device(
                    device_id=device_id,
                    device_remark=device_remark,
                    product_key=product_key,
                    device_type=device_type
                )

                db.session.add(device)
                db.session.flush()  # 获取设备ID但不提交

                results.append({
                    "device_id": device_id,
                    "status": "success",
                    "error": ""
                })
                success_count += 1

            except Exception as e:
                results.append({
                    "device_id": device_id,
                    "status": "failed",
                    "error": str(e)
                })
                failed_count += 1

        # 提交所有成功的设备
        if success_count > 0:
            db.session.commit()
        else:
            db.session.rollback()

        message = f"批量添加完成：成功 {success_count} 个，失败 {failed_count} 个"

        return jsonify({
            "success": success_count > 0,
            "message": message,
            "results": results,
            "summary": {
                "total": len(device_ids),
                "success": success_count,
                "failed": failed_count
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "message": f"批量添加设备失败：{str(e)}"}), 500


@device_bp.route("/device/edit/<int:id>", methods=["GET", "POST"])
@login_required
def edit_device(id):
    """编辑设备"""
    device = Device.query.get_or_404(id)

    if request.method == "POST":
        device_id = request.form.get("device_id")
        device_remark = request.form.get("device_remark")
        product_key = request.form.get("product_key")
        device_type = request.form.get("device_type")

        # 检查设备ID是否已被其他设备使用
        existing_device = Device.query.filter_by(device_id=device_id).first()
        if existing_device and existing_device.id != id:
            flash("设备ID已存在", "danger")
            return redirect(url_for("device.edit_device", id=id))

        # 处理设备类型
        if device_type:
            try:
                device_type = int(device_type)
                if device_type not in [10, 50, 51]:
                    flash("无效的设备类型", "danger")
                    return redirect(url_for("device.edit_device", id=id))
            except ValueError:
                flash("设备类型格式错误", "danger")
                return redirect(url_for("device.edit_device", id=id))
        else:
            device_type = None

        # 更新设备信息
        device.device_id = device_id
        device.device_remark = device_remark
        device.product_key = product_key
        device.device_type = device_type

        db.session.commit()

        flash("设备更新成功", "success")
        return redirect(url_for("device.devices"))

    return render_template("device_form.html", device=device)


@device_bp.route("/api/device/edit/<int:id>", methods=["PUT"])
@login_required
def edit_device_api(id):
    """Ajax编辑设备API"""
    try:
        device = Device.query.get_or_404(id)
        data = request.get_json()

        device_id = data.get("device_id", "").strip()
        device_remark = data.get("device_remark", "").strip()
        product_key = data.get("product_key", "").strip()

        if not device_id or not product_key:
            return jsonify({"success": False, "message": "设备ID和产品密钥不能为空"}), 400

        # 检查设备ID是否已被其他设备使用
        existing_device = Device.query.filter_by(device_id=device_id).first()
        if existing_device and existing_device.id != id:
            return jsonify({"success": False, "message": "设备ID已存在"}), 400

        # 更新设备信息
        device.device_id = device_id
        device.device_remark = device_remark
        device.product_key = product_key

        db.session.commit()

        return jsonify(
            {
                "success": True,
                "message": "设备更新成功",
                "device": {
                    "id": device.id,
                    "device_id": device.device_id,
                    "device_remark": device.device_remark,
                    "product_key": device.product_key,
                },
            }
        )

    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "message": f"更新设备失败：{str(e)}"}), 500


@device_bp.route("/device/delete/<int:id>")
@login_required
def delete_device(id):
    """删除设备"""
    device = Device.query.get_or_404(id)

    # 删除相关的OTA任务
    OtaTask.query.filter_by(device_id=id).delete()

    db.session.delete(device)
    db.session.commit()

    flash("设备删除成功", "success")
    return redirect(url_for("device.devices"))


@device_bp.route("/api/device/delete/<int:id>", methods=["DELETE"])
@login_required
def delete_device_api(id):
    """Ajax删除设备API"""
    try:
        device = Device.query.get_or_404(id)

        # 删除相关的OTA任务
        OtaTask.query.filter_by(device_id=id).delete()

        db.session.delete(device)
        db.session.commit()

        return jsonify({"success": True, "message": "设备删除成功"})

    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "message": f"删除设备失败：{str(e)}"}), 500


@device_bp.route("/batch_import_devices", methods=["POST"])
@login_required
def batch_import_devices():
    """批量导入设备"""
    if "import_file" not in request.files:
        return jsonify({"error": "没有上传文件"}), 400

    file = request.files["import_file"]
    if file.filename == "":
        return jsonify({"error": "没有选择文件"}), 400

    if not file.filename.endswith((".xlsx", ".xls")):
        return jsonify({"error": "请上传Excel文件"}), 400

    try:
        # 读取Excel文件
        df = pd.read_excel(file)

        # 验证必要的列是否存在
        required_columns = ["设备ID", "产品密钥"]
        if not all(col in df.columns for col in required_columns):
            return jsonify({"error": "Excel文件格式不正确，请使用正确的模板"}), 400

        results = []
        success_count = 0
        existing_count = 0
        failed_count = 0

        # 处理每一行数据
        for index, row in df.iterrows():
            device_id = str(row["设备ID"]).strip()
            product_key = str(row["产品密钥"]).strip()
            device_remark = str(row.get("设备备注", "")).strip() if "设备备注" in df.columns else ""

            # 验证数据
            if not device_id or not product_key:
                results.append({"device_id": device_id, "status": "failed", "message": "设备ID和产品密钥不能为空"})
                failed_count += 1
                continue

            # 检查设备是否已存在
            existing_device = Device.query.filter_by(device_id=device_id).first()
            if existing_device:
                results.append({"device_id": device_id, "status": "existing", "message": "设备已存在"})
                existing_count += 1
                continue

            try:
                # 创建新设备
                new_device = Device(device_id=device_id, product_key=product_key, device_remark=device_remark)
                db.session.add(new_device)
                db.session.commit()

                results.append({"device_id": device_id, "status": "success", "message": "导入成功"})
                success_count += 1

            except Exception as e:
                db.session.rollback()
                results.append({"device_id": device_id, "status": "failed", "message": f"导入失败：{str(e)}"})
                failed_count += 1

        return jsonify(
            {
                "success_count": success_count,
                "existing_count": existing_count,
                "failed_count": failed_count,
                "results": results,
            }
        )

    except Exception as e:
        return jsonify({"error": f"处理文件时发生错误：{str(e)}"}), 500


@device_bp.route("/api/devices/batch-update-product-key", methods=["POST"])
@login_required
def batch_update_product_key():
    """批量修改设备产品密钥"""
    try:
        data = request.get_json()
        device_ids = data.get("device_ids", [])
        new_product_key = data.get("new_product_key", "").strip()

        if not device_ids:
            return jsonify({"success": False, "message": "请选择要修改的设备"}), 400

        if not new_product_key:
            return jsonify({"success": False, "message": "新产品密钥不能为空"}), 400

        results = []
        success_count = 0
        failed_count = 0

        # 处理每个设备
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        "device_id": f"ID:{device_id}",
                        "device_remark": "未知",
                        "old_product_key": "未知",
                        "new_product_key": new_product_key,
                        "status": "failed",
                        "error": "设备不存在"
                    })
                    failed_count += 1
                    continue

                # 记录原产品密钥
                old_product_key = device.product_key

                # 更新产品密钥
                device.product_key = new_product_key
                # device.updated_at = datetime.now()

                db.session.commit()

                results.append({
                    "device_id": device.device_id,
                    "device_remark": device.device_remark or "",
                    "old_product_key": old_product_key,
                    "new_product_key": new_product_key,
                    "status": "success",
                    "error": ""
                })
                success_count += 1

            except Exception as e:
                db.session.rollback()
                # 尝试获取设备信息用于错误报告
                try:
                    device = Device.query.get(device_id)
                    device_info = {
                        "device_id": device.device_id if device else f"ID:{device_id}",
                        "device_remark": device.device_remark if device else "未知",
                        "old_product_key": device.product_key if device else "未知"
                    }
                except:
                    device_info = {
                        "device_id": f"ID:{device_id}",
                        "device_remark": "未知",
                        "old_product_key": "未知"
                    }

                results.append({
                    **device_info,
                    "new_product_key": new_product_key,
                    "status": "failed",
                    "error": str(e)
                })
                failed_count += 1

        # 构建响应消息
        total_count = len(device_ids)
        if success_count == total_count:
            message = f"批量修改成功！共修改 {success_count} 个设备的产品密钥"
        elif success_count > 0:
            message = f"部分修改成功！成功修改 {success_count} 个设备，失败 {failed_count} 个设备"
        else:
            message = f"批量修改失败！{failed_count} 个设备修改失败"

        return jsonify({
            "success": success_count > 0,
            "message": message,
            "results": results,
            "summary": {
                "total": total_count,
                "success": success_count,
                "failed": failed_count
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "message": f"批量修改失败：{str(e)}"}), 500


@device_bp.route("/download_import_template")
@login_required
def download_import_template():
    """下载导入模板"""
    try:
        # 创建示例数据
        data = {
            "设备ID": ["DEVICE001", "DEVICE002"],
            "产品密钥": ["PRODUCT_KEY_1", "PRODUCT_KEY_2"],
            "设备备注": ["示例设备1", "示例设备2"],
        }
        df = pd.DataFrame(data)

        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, index=False, sheet_name="设备导入模板")

            # 获取工作表
            worksheet = writer.sheets["设备导入模板"]

            # 设置列宽
            worksheet.column_dimensions["A"].width = 20
            worksheet.column_dimensions["B"].width = 30
            worksheet.column_dimensions["C"].width = 30

            # 添加说明
            worksheet["A1"].comment = openpyxl.comments.Comment("设备的唯一标识符，必填", "系统")
            worksheet["B1"].comment = openpyxl.comments.Comment("设备的产品密钥，必填", "系统")
            worksheet["C1"].comment = openpyxl.comments.Comment("设备的备注信息，选填", "系统")

        output.seek(0)
        return send_file(
            output,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            as_attachment=True,
            download_name="设备导入模板.xlsx",
        )

    except Exception as e:
        return jsonify({"error": f"生成模板时发生错误：{str(e)}"}), 500
