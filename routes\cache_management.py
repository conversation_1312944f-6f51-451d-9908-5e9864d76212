#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存管理路由
提供缓存监控和管理功能
"""

from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required
from services.cache_service import cache_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
cache_management_bp = Blueprint('cache_management', __name__)

@cache_management_bp.route('/admin/cache')
@login_required
def cache_dashboard():
    """缓存管理仪表板"""
    return render_template('admin/cache_dashboard.html')

@cache_management_bp.route('/api/cache/stats')
@login_required
def get_cache_stats():
    """获取缓存统计信息"""
    try:
        stats = cache_service.get_cache_stats()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@cache_management_bp.route('/api/cache/clear', methods=['POST'])
@login_required
def clear_cache():
    """清除缓存"""
    try:
        data = request.get_json() or {}
        cache_type = data.get('type', 'all')

        if cache_type == 'all':
            result = cache_service.clear_all()
            message = "所有缓存已清除"
        elif cache_type == 'device':
            result = cache_service.invalidate_device_cache()
            message = "设备相关缓存已清除"
        elif cache_type == 'ota':
            result = cache_service.invalidate_ota_cache()
            message = "OTA任务相关缓存已清除"
        elif cache_type == 'firmware':
            result = cache_service.invalidate_firmware_cache()
            message = "固件相关缓存已清除"
        else:
            return jsonify({
                'success': False,
                'error': '无效的缓存类型'
            }), 400

        if result:
            logger.info(f"缓存清除成功: {cache_type}")
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': '缓存清除失败'
            }), 500

    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@cache_management_bp.route('/api/cache/info')
@login_required
def get_cache_info():
    """获取缓存详细信息"""
    try:
        # 这里可以添加更详细的缓存信息
        # 比如缓存大小、键数量等
        info = {
            'cache_type': 'SimpleCache',  # 从配置中获取
            'description': '简单内存缓存，适用于单机部署',
            'features': [
                '内存存储',
                '自动过期',
                '线程安全'
            ],
            'limitations': [
                '不支持持久化',
                '不支持集群',
                '重启后数据丢失'
            ],
            'recommendations': [
                '生产环境建议使用Redis',
                '定期监控缓存命中率',
                '合理设置缓存过期时间'
            ]
        }

        return jsonify({
            'success': True,
            'info': info
        })

    except Exception as e:
        logger.error(f"获取缓存信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
