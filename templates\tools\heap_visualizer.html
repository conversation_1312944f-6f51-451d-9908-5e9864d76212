<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>堆内存可视化工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 300;
            letter-spacing: 2px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .input-section {
            margin-bottom: 20px;
        }
        
        .input-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-weight: 500;
        }
        
        textarea {
            width: 100%;
            height: 180px;
            font-family: 'Fira Code', 'Consolas', monospace;
            font-size: 13px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 15px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            resize: vertical;
        }
        
        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .input-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .input-controls > * {
            flex: 1;
            min-width: 120px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .zoom-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .zoom-btn:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }
        
        .zoom-level {
            font-weight: 500;
            color: #333;
            min-width: 60px;
            text-align: center;
        }
        
        .legend {
            display: flex;
            gap: 25px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        
        .legend-color {
            width: 24px;
            height: 16px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .heap-block {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .heap-block:hover {
            filter: brightness(1.2);
            transform: scale(1.02);
        }

        .heap-container {
            margin-bottom: 30px;
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.98);
        }

        .heap-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .heap-title .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .heap-title .remove-btn:hover {
            background: #c82333;
        }

        .heap-name-input {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 14px;
            background: transparent;
            color: #333;
            max-width: 200px;
        }

        .add-heap-section {
            background: rgba(108, 117, 125, 0.1);
            border: 2px dashed #6c757d;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-bottom: 25px;
        }

        .examples-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .examples-header {
            padding: 15px 20px;
            background: linear-gradient(45deg, #17a2b8, #20c997);
            color: white;
            border-radius: 10px 10px 0 0;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .examples-header:hover {
            background: linear-gradient(45deg, #138496, #1e7e34);
        }

        .examples-content {
            padding: 20px;
            display: none;
            border-top: 1px solid #dee2e6;
        }

        .examples-content.show {
            display: block;
        }

        .example-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .example-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .example-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .example-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .example-desc {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .style-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .style-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }

        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .zoom-btn:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }

        .zoom-level {
            font-weight: 500;
            color: #333;
            min-width: 60px;
            text-align: center;
        }
        
        .visualization-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .heap-info {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-family: 'Fira Code', monospace;
            font-size: 14px;
            color: #495057;
            border-left: 4px solid #667eea;
        }
        
        .canvas-container {
            position: relative;
            overflow: auto;
            border: 2px solid #dee2e6;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            cursor: grab;
            max-height: 600px;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .canvas-container:active {
            cursor: grabbing;
        }

        .canvas-container:hover {
            border-color: #667eea;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        #heapCanvas {
            display: block;
            transition: transform 0.1s ease;
        }
        
        .tooltip {
            position: fixed;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            font-size: 12px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            pointer-events: none;
            z-index: 1000;
            max-width: 350px;
            line-height: 1.5;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transform: translate(-72px, -150px);
        }
        
        .tooltip::before {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid transparent;
        }
        
        .stat-card.free {
            border-left-color: #28a745;
        }
        
        .stat-card.allocated {
            border-left-color: #dc3545;
        }
        
        .stat-card.total {
            border-left-color: #667eea;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .legend {
                justify-content: center;
            }
            
            .button-group {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 堆内存可视化分析器</h1>
    </div>
    
    <div class="container">
        <div class="control-panel">
            <div class="input-section">
                <h3>📊 输入堆分析数据</h3>

                <!-- 风格选择器 -->
                <div class="style-selector">
                    <label for="styleSelect">🎨 可视化风格:</label>
                    <select id="styleSelect" onchange="updateVisualizationStyle()">
                        <option value="gradient">渐变风格</option>
                        <option value="flat">扁平风格</option>
                        <option value="neon">霓虹风格</option>
                        <option value="classic">经典风格</option>
                        <option value="glass">玻璃风格</option>
                        <option value="material">材质风格</option>
                        <option value="cyberpunk">赛博朋克</option>
                        <option value="minimal">极简风格</option>
                        <option value="retro">复古风格</option>
                    </select>
                </div>

                <div class="input-controls">
                    <input type="text" id="heapName" placeholder="堆名称 (可选)" class="heap-name-input" value="堆内存 #1">
                    <button type="button" class="btn btn-primary" onclick="addHeapData()">➕ 添加堆数据</button>
                    <button type="button" class="btn btn-secondary" onclick="clearAllData()">🗑️ 清除所有</button>
                </div>
                <textarea id="dataInput" placeholder="粘贴堆分析输出数据..."></textarea>
                <div class="button-group">
                    <button type="button" class="btn btn-success" onclick="exportAllSVG()">📄 导出所有SVG</button>
                    <button type="button" class="btn btn-info" onclick="exportAllPNG()">🖼️ 导出所有PNG</button>
                </div>
            </div>
        </div>

        <!-- 示例数据折叠菜单 -->
        <div class="examples-section">
            <div class="examples-header" onclick="toggleExamples()">
                <span>📝 示例数据</span>
                <span id="examplesToggle">▼</span>
            </div>
            <div class="examples-content" id="examplesContent">
                <p>选择一个示例数据快速开始分析：</p>
                <div class="example-grid">
                    <div class="example-card" onclick="loadExample(1)">
                        <div class="example-title">🟢 初始状态</div>
                        <div class="example-desc">典型的初始堆内存布局，少量分配和大量空闲空间</div>
                        <button type="button" class="btn btn-info btn-sm">加载示例</button>
                    </div>
                    <div class="example-card" onclick="loadExample(2)">
                        <div class="example-title">🔴 内存碎片化</div>
                        <div class="example-desc">严重碎片化的堆内存，多个小块交替分布</div>
                        <button type="button" class="btn btn-info btn-sm">加载示例</button>
                    </div>
                    <div class="example-card" onclick="loadExample(3)">
                        <div class="example-title">🟡 大块分配</div>
                        <div class="example-desc">大块内存分配模式，适合测试大内存可视化</div>
                        <button type="button" class="btn btn-info btn-sm">加载示例</button>
                    </div>
                    <div class="example-card" onclick="loadExample(4)">
                        <div class="example-title">🟠 混合使用</div>
                        <div class="example-desc">混合使用模式，包含不同大小的分配块</div>
                        <button type="button" class="btn btn-info btn-sm">加载示例</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="add-heap-section" id="addHeapSection">
            <h4>📋 添加新的堆内存数据进行对比分析</h4>
            <p>在上方输入框中粘贴堆分析数据，设置名称后点击"添加堆数据"按钮，或使用上方的示例数据快速开始</p>
        </div>

        <div id="heapContainers">
            <!-- 动态生成的堆容器将在这里显示 -->
        </div>
    </div>
    
    <div id="tooltip" class="tooltip" style="display: none;"></div>

    <script>
        let heapDataList = [];
        let heapCounter = 1;
        let hoveredBlock = null;
        let currentStyle = 'gradient';
        let canvasScales = {}; // 存储每个canvas的缩放状态
        let canvasPans = {}; // 存储每个canvas的平移状态

        function addHeapData() {
            const input = document.getElementById('dataInput').value.trim();
            const name = document.getElementById('heapName').value.trim() || `堆内存 #${heapCounter}`;

            if (!input) {
                alert('请输入堆分析数据');
                return;
            }

            try {
                const heapData = parseHeapData(input);
                if (!heapData.range) {
                    alert('无效的堆数据格式');
                    return;
                }

                heapData.name = name;
                heapData.id = `heap_${Date.now()}`;
                heapDataList.push(heapData);

                createHeapVisualization(heapData);

                // 清空输入框并更新计数器
                document.getElementById('dataInput').value = '';
                heapCounter++;
                document.getElementById('heapName').value = `堆内存 #${heapCounter}`;

                // 隐藏添加提示
                document.getElementById('addHeapSection').style.display = 'none';

            } catch (e) {
                alert('数据解析错误: ' + e.message);
            }
        }

        function createHeapVisualization(heapData) {
            const container = document.getElementById('heapContainers');

            const heapDiv = document.createElement('div');
            heapDiv.className = 'heap-container';
            heapDiv.id = heapData.id;

            // 初始化缩放和平移状态
            canvasScales[heapData.id] = 1;
            canvasPans[heapData.id] = { x: 0, y: 0 };

            heapDiv.innerHTML = `
                <div class="heap-title">
                    <input type="text" value="${heapData.name}" class="heap-name-input" onchange="updateHeapName('${heapData.id}', this.value)">
                    <button class="remove-btn" onclick="removeHeap('${heapData.id}')" title="删除此堆">×</button>
                </div>

                <div class="controls">
                    <div class="legend">
                        <div class="legend-item">
                            <span class="legend-color" style="background: linear-gradient(45deg, #28a745, #20c997);"></span>
                            空闲区域 (FREE)
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: linear-gradient(45deg, #dc3545, #fd7e14);"></span>
                            已分配区域 (ALLOCATED)
                        </div>
                    </div>

                    <div class="zoom-controls">
                        <button type="button" class="zoom-btn" onclick="zoomOut('${heapData.id}')">−</button>
                        <span class="zoom-level" id="zoomLevel_${heapData.id}">100%</span>
                        <button type="button" class="zoom-btn" onclick="zoomIn('${heapData.id}')">+</button>
                    </div>
                </div>

                <div class="heap-info" id="heapInfo_${heapData.id}"></div>

                <div class="canvas-container" id="canvasContainer_${heapData.id}">
                    <canvas id="canvas_${heapData.id}"></canvas>
                </div>

                <div class="stats" id="stats_${heapData.id}"></div>
            `;

            container.appendChild(heapDiv);

            // 绘制堆可视化
            drawHeap(heapData);
            updateInfo(heapData);
            setupCanvasEvents(heapData);
        }

        function setupCanvasEvents(heapData) {
            const canvas = document.getElementById(`canvas_${heapData.id}`);
            const container = document.getElementById(`canvasContainer_${heapData.id}`);

            let isDragging = false;
            let lastMouseX = 0;
            let lastMouseY = 0;

            // 鼠标事件
            canvas.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    const deltaX = e.clientX - lastMouseX;
                    const deltaY = e.clientY - lastMouseY;

                    canvasPans[heapData.id].x += deltaX;
                    canvasPans[heapData.id].y += deltaY;

                    updateCanvasTransform(heapData.id);

                    lastMouseX = e.clientX;
                    lastMouseY = e.clientY;
                } else {
                    // 使用全局坐标进行块检测
                    const block = getBlockAtPosition(e.clientX, e.clientY, heapData, canvas);

                    if (block !== hoveredBlock) {
                        hoveredBlock = block;
                        drawHeap(heapData);

                        if (block) {
                            showTooltip(e, block, heapData);
                        } else {
                            hideTooltip();
                        }
                    }
                }
            });

            canvas.addEventListener('mousedown', (e) => {
                isDragging = true;
                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
                canvas.style.cursor = 'grabbing';
            });

            canvas.addEventListener('mouseup', () => {
                isDragging = false;
                canvas.style.cursor = 'grab';
            });

            canvas.addEventListener('mouseleave', () => {
                isDragging = false;
                canvas.style.cursor = 'grab';
                hideTooltip();
            });

            // 滚轮缩放
            canvas.addEventListener('wheel', (e) => {
                e.preventDefault();

                const rect = canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                const newScale = Math.max(0.1, Math.min(5, canvasScales[heapData.id] * delta));

                const scaleChange = newScale / canvasScales[heapData.id];
                canvasPans[heapData.id].x = mouseX - (mouseX - canvasPans[heapData.id].x) * scaleChange;
                canvasPans[heapData.id].y = mouseY - (mouseY - canvasPans[heapData.id].y) * scaleChange;

                canvasScales[heapData.id] = newScale;
                updateCanvasTransform(heapData.id);
                updateZoomLevel(heapData.id);
            });

            // 设置初始样式
            canvas.style.cursor = 'grab';
        }

        function showTooltip(e, block, heapData) {
            const tooltip = document.getElementById('tooltip');
            tooltip.style.display = 'block';
            tooltip.style.position = 'fixed';
            tooltip.style.left = e.clientX + 'px';
            tooltip.style.top  = e.clientY + 'px';
            tooltip.style.transform = 'translate(-72ypx, -150px)';

            const utilization = ((block.size / heapData.range.size) * 100).toFixed(2);
            const sizeKB = (block.size / 1024).toFixed(1);
            const blockTypeIcon = block.type === 'free' ? '🟢' : (block.type === 'alloc' ? '�' : '🔴');
            const blockTypeName = block.type === 'free' ? '空闲区域 (FREE)' : (block.type === 'alloc' ? '已分配区域 (ALLOC)' : '间隙 (GAP)');

            tooltip.innerHTML = `
                <div style="border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 8px; margin-bottom: 8px;">
                    <strong>${blockTypeIcon} ${blockTypeName}</strong>
                </div>
                <div style="display: grid; grid-template-columns: auto 1fr; gap: 8px; font-size: 11px;">
                    <span>起始地址:</span><span style="font-family: monospace;">0x${block.start.toString(16).toUpperCase()}</span>
                    <span>结束地址:</span><span style="font-family: monospace;">0x${block.end.toString(16).toUpperCase()}</span>
                    <span>大小:</span><span>${block.size.toLocaleString()} 字节 (${sizeKB} KB)</span>
                    <span>占用比例:</span><span>${utilization}%</span>
                    <span>地址范围:</span><span style="font-family: monospace;">${(block.end - block.start).toLocaleString()} 字节</span>
                </div>
            `;
        }

        function hideTooltip() {
            document.getElementById('tooltip').style.display = 'none';
        }

        function getBlockAtPosition(x, y, heapData, canvas) {
            // 获取画布的边界矩形
            const rect = canvas.getBoundingClientRect();

            // 计算相对于画布的坐标
            const canvasX = x - rect.left;
            const canvasY = y - rect.top;

            // 考虑画布的显示尺寸与实际尺寸的比例
            const scaleX = canvas.width / canvas.offsetWidth;
            const scaleY = canvas.height / canvas.offsetHeight;

            const actualX = canvasX * scaleX;
            const actualY = canvasY * scaleY;

            // 考虑缩放和平移
            const scale = canvasScales[heapData.id] || 1;
            const pan = canvasPans[heapData.id] || {x: 0, y: 0};

            const adjustedX = (actualX - pan.x) / scale;
            const adjustedY = (actualY - pan.y) / scale;

            // 检查Y轴范围（内存块区域）
            if (adjustedY < 100 || adjustedY > 300) return null;

            const padding = 40;
            const maxWidth = 1800 - (padding * 2);

            // 计算地址到像素的映射（与绘制逻辑一致）
            const heapStart = heapData.range.start;
            const heapSize = heapData.range.size;
            const pixelsPerByte = maxWidth / heapSize;

            // 检查每个内存块
            for (const block of heapData.blocks) {
                const blockStartOffset = block.start - heapStart;
                const blockX = padding + (blockStartOffset * pixelsPerByte);
                const blockWidth = Math.max(2, block.size * pixelsPerByte);

                // 为小块扩大点击区域
                const hitAreaExpansion = blockWidth < 10 ? 5 : 0;
                const hitX1 = blockX - hitAreaExpansion;
                const hitX2 = blockX + blockWidth + hitAreaExpansion;

                if (adjustedX >= hitX1 && adjustedX <= hitX2) {
                    return block;
                }
            }
            return null;
        }

        function updateCanvasTransform(heapId) {
            const canvas = document.getElementById(`canvas_${heapId}`);
            const scale = canvasScales[heapId];
            const pan = canvasPans[heapId];
            canvas.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${scale})`;
        }

        function zoomIn(heapId) {
            canvasScales[heapId] = Math.min(5, canvasScales[heapId] * 1.2);
            updateCanvasTransform(heapId);
            updateZoomLevel(heapId);
        }

        function zoomOut(heapId) {
            canvasScales[heapId] = Math.max(0.1, canvasScales[heapId] * 0.8);
            updateCanvasTransform(heapId);
            updateZoomLevel(heapId);
        }

        function updateZoomLevel(heapId) {
            const zoomElement = document.getElementById(`zoomLevel_${heapId}`);
            if (zoomElement) {
                zoomElement.textContent = Math.round(canvasScales[heapId] * 100) + '%';
            }
        }

        function parseHeapData(text) {
            const lines = text.trim().split('\n');
            const data = { range: null, blocks: [] };

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine || trimmedLine.startsWith('HEAP_END')) continue;

                if (trimmedLine.startsWith('HEAP_RANGE:')) {
                    const parts = trimmedLine.substring(11).split(',');
                    data.range = {
                        start: parseInt(parts[0], 16),
                        end: parseInt(parts[1], 16),
                        size: parseInt(parts[2])
                    };
                } else if (trimmedLine.startsWith('HEAP_FREE:')) {
                    const parts = trimmedLine.substring(10).split(',');
                    const start = parseInt(parts[0], 16);
                    const end = parseInt(parts[1], 16);
                    const size = parseInt(parts[2]);

                    if (!isNaN(start) && !isNaN(end) && !isNaN(size) && start < end) {
                        data.blocks.push({
                            type: 'free',
                            start: start,
                            end: end,
                            size: size
                        });
                    }
                } else if (trimmedLine.startsWith('HEAP_ALLOC:')) {
                    const parts = trimmedLine.substring(11).split(',');
                    const start = parseInt(parts[0], 16);
                    const end = parseInt(parts[1], 16);
                    const size = parseInt(parts[2]);

                    if (!isNaN(start) && !isNaN(end) && !isNaN(size) && start < end) {
                        data.blocks.push({
                            type: 'alloc',
                            start: start,
                            end: end,
                            size: size
                        });
                    }
                }
            }

            // 按地址排序
            data.blocks.sort((a, b) => a.start - b.start);

            console.log('解析的内存块:', data.blocks);

            return data;
        }

        function drawHeap(heapData) {
            if (!heapData) return;

            const canvas = document.getElementById(`canvas_${heapData.id}`);
            const ctx = canvas.getContext('2d');

            // 大幅提高分辨率和清晰度
            const dpr = window.devicePixelRatio || 1;
            const width = 1800;
            const height = 400;

            // 设置高DPI支持
            canvas.width = width * dpr;
            canvas.height = height * dpr;
            canvas.style.width = width + 'px';
            canvas.style.height = height + 'px';
            ctx.scale(dpr, dpr);

            // 启用抗锯齿
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';

            ctx.clearRect(0, 0, width, height);

            // Draw background
            drawBackground(ctx, width, height);

            // 绘制网格线帮助定位
            drawGrid(ctx, width, height);

            // 按地址位置绘制内存块
            const padding = 40;
            const blockHeight = 200;
            const blockY = 100;
            const maxWidth = width - (padding * 2);

            // 计算地址到像素的映射
            const heapStart = heapData.range.start;
            const heapSize = heapData.range.size;
            const pixelsPerByte = maxWidth / heapSize;

            console.log(`堆起始地址: 0x${heapStart.toString(16)}, 大小: ${heapSize}, 像素/字节: ${pixelsPerByte}`);

            // 首先绘制整个堆的背景（灰色）
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(padding, blockY, maxWidth, blockHeight);

            // 绘制边框
            ctx.strokeStyle = '#adb5bd';
            ctx.lineWidth = 2;
            ctx.strokeRect(padding, blockY, maxWidth, blockHeight);

            // 绘制每个内存块
            for (let i = 0; i < heapData.blocks.length; i++) {
                const block = heapData.blocks[i];

                // 计算块在画布上的位置
                const blockStartOffset = block.start - heapStart;
                const blockX = padding + (blockStartOffset * pixelsPerByte);
                const blockWidth = Math.max(2, block.size * pixelsPerByte); // 最小2像素宽度

                console.log(`块 ${i}: 类型=${block.type}, 地址=0x${block.start.toString(16)}, 大小=${block.size}, X=${blockX}, 宽度=${blockWidth}`);

                // 检查是否是悬浮的块
                const isHovered = hoveredBlock === block;

                // 根据风格绘制块
                drawBlock(ctx, blockX, blockY, blockWidth, blockHeight, block, isHovered, i);
            }

            // Draw detailed address labels and info
            drawAddressLabels(ctx, heapData, width, height, padding);

            // Draw memory usage bar
            drawMemoryUsageBar(ctx, heapData, width, height);
        }

        function drawBackground(ctx, width, height) {
            const gradient = ctx.createLinearGradient(0, 0, 0, height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
        }

        function drawGrid(ctx, width, height) {
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.lineWidth = 1;

            // 垂直网格线
            for (let x = 40; x < width - 40; x += 50) {
                ctx.beginPath();
                ctx.moveTo(x, 80);
                ctx.lineTo(x, 320);
                ctx.stroke();
            }

            // 水平网格线
            for (let y = 100; y <= 300; y += 50) {
                ctx.beginPath();
                ctx.moveTo(40, y);
                ctx.lineTo(width - 40, y);
                ctx.stroke();
            }
        }

        function drawAddressLabels(ctx, heapData, width, height, padding) {
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 14px Fira Code, monospace';

            // 起始地址
            ctx.textAlign = 'left';
            ctx.fillText(`起始: 0x${heapData.range.start.toString(16).toUpperCase()}`, padding, 70);

            // 结束地址
            ctx.textAlign = 'right';
            ctx.fillText(`结束: 0x${heapData.range.end.toString(16).toUpperCase()}`, width - padding, 70);

            // 总大小
            ctx.textAlign = 'center';
            ctx.fillText(`总大小: ${heapData.range.size.toLocaleString()} 字节 (${(heapData.range.size / 1024).toFixed(1)} KB)`, width / 2, 70);
        }

        function drawMemoryUsageBar(ctx, heapData, width, height) {
            const barY = height - 60;
            const barHeight = 20;
            const barWidth = width - 80;
            const barX = 40;

            // 背景
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(barX, barY, barWidth, barHeight);

            // 计算使用率
            let allocatedSize = 0;
            heapData.blocks.forEach(block => {
                if (block.type === 'alloc') {
                    allocatedSize += block.size;
                }
            });

            const usageRatio = allocatedSize / heapData.range.size;
            const usageWidth = barWidth * usageRatio;

            // 使用率条
            const gradient = ctx.createLinearGradient(barX, barY, barX + usageWidth, barY);
            if (usageRatio < 0.5) {
                gradient.addColorStop(0, '#28a745');
                gradient.addColorStop(1, '#20c997');
            } else if (usageRatio < 0.8) {
                gradient.addColorStop(0, '#ffc107');
                gradient.addColorStop(1, '#fd7e14');
            } else {
                gradient.addColorStop(0, '#dc3545');
                gradient.addColorStop(1, '#c82333');
            }

            ctx.fillStyle = gradient;
            ctx.fillRect(barX, barY, usageWidth, barHeight);

            // 使用率文字
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`内存使用率: ${(usageRatio * 100).toFixed(1)}%`, width / 2, barY + barHeight + 20);
        }

        function drawBlock(ctx, x, y, width, height, block, isHovered, index) {
            const style = currentStyle;

            // 为小块添加特殊处理
            const isSmallBlock = width < 30;
            const adjustedHeight = isSmallBlock && isHovered ? height + 20 : height;
            const adjustedY = isSmallBlock && isHovered ? y - 10 : y;

            switch (style) {
                case 'gradient':
                    drawGradientBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                case 'flat':
                    drawFlatBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                case 'neon':
                    drawNeonBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                case 'classic':
                    drawClassicBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                case 'glass':
                    drawGlassBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                case 'material':
                    drawMaterialBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                case 'cyberpunk':
                    drawCyberpunkBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                case 'minimal':
                    drawMinimalBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                case 'retro':
                    drawRetroBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
                    break;
                default:
                    drawGradientBlock(ctx, x, adjustedY, width, adjustedHeight, block, isHovered);
            }

            // 改进的文字渲染
            drawBlockLabel(ctx, x, adjustedY, width, adjustedHeight, block, isHovered, style);

            // 为小块添加指示器
            if (isSmallBlock) {
                drawSmallBlockIndicator(ctx, x, y, width, height, block, isHovered);
            }
        }

        function drawBlockLabel(ctx, x, y, width, height, block, isHovered, style) {
            const centerX = x + width / 2;
            const centerY = y + height / 2;

            // 根据块大小调整文字
            if (width > 80) {
                // 大块：显示完整信息
                ctx.fillStyle = getTextColor(style, block.type);
                ctx.font = isHovered ? 'bold 14px Arial' : 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${block.size}B`, centerX, centerY - 5);
                ctx.font = '10px Arial';
                ctx.fillText(block.type === 'free' ? 'FREE' : 'ALLOC', centerX, centerY + 10);
            } else if (width > 40) {
                // 中块：显示大小
                ctx.fillStyle = getTextColor(style, block.type);
                ctx.font = isHovered ? 'bold 12px Arial' : 'bold 10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${block.size}B`, centerX, centerY + 3);
            } else if (width > 20) {
                // 小块：显示简化信息
                ctx.fillStyle = getTextColor(style, block.type);
                ctx.font = 'bold 8px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${block.size}`, centerX, centerY + 2);
            }
            // 极小块不显示文字，但有指示器
        }

        function drawSmallBlockIndicator(ctx, x, y, width, height, block, isHovered) {
            if (isHovered) {
                // 绘制指示线
                ctx.strokeStyle = block.type === 'free' ? '#28a745' : '#dc3545';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x + width / 2, y);
                ctx.lineTo(x + width / 2, y - 30);
                ctx.stroke();

                // 绘制信息框
                const infoX = x + width / 2 - 40;
                const infoY = y - 50;
                const infoWidth = 80;
                const infoHeight = 20;

                ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                ctx.fillRect(infoX, infoY, infoWidth, infoHeight);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${block.size}B ${block.type.toUpperCase()}`, infoX + infoWidth / 2, infoY + 14);
            }
        }

        function getTextColor(style, blockType) {
            switch (style) {
                case 'neon':
                    return blockType === 'free' ? '#00ffff' : '#ff00ff';
                case 'cyberpunk':
                    return '#00ff00';
                case 'minimal':
                    return '#333';
                case 'retro':
                    return '#fff';
                default:
                    return 'white';
            }
        }

        function updateInfo(heapData) {
            if (!heapData) return;

            const infoDiv = document.getElementById(`heapInfo_${heapData.id}`);
            infoDiv.innerHTML = `
                📍 堆地址范围: 0x${heapData.range.start.toString(16).toUpperCase()} - 0x${heapData.range.end.toString(16).toUpperCase()}<br>
                📏 总大小: ${heapData.range.size.toLocaleString()} 字节 (${(heapData.range.size / 1024).toFixed(1)} KB)
            `;

            // Update statistics
            let freeSize = 0, allocSize = 0, freeCount = 0, allocCount = 0;

            heapData.blocks.forEach(block => {
                if (block.type === 'free') {
                    freeSize += block.size;
                    freeCount++;
                } else {
                    allocSize += block.size;
                    allocCount++;
                }
            });

            const statsDiv = document.getElementById(`stats_${heapData.id}`);
            statsDiv.innerHTML = `
                <div class="stat-card total">
                    <div class="stat-value">${heapData.range.size.toLocaleString()}</div>
                    <div class="stat-label">总字节数</div>
                </div>
                <div class="stat-card free">
                    <div class="stat-value">${freeSize.toLocaleString()}</div>
                    <div class="stat-label">空闲字节 (${freeCount} 块)</div>
                </div>
                <div class="stat-card allocated">
                    <div class="stat-value">${allocSize.toLocaleString()}</div>
                    <div class="stat-label">已分配字节 (${allocCount} 块)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${((allocSize / heapData.range.size) * 100).toFixed(1)}%</div>
                    <div class="stat-label">利用率</div>
                </div>
            `;
        }

        function drawGradientBlock(ctx, x, y, width, height, block, isHovered) {
            const gradient = ctx.createLinearGradient(x, y, x, y + height);
            if (block.type === 'free') {
                // FREE区域 - 绿色
                if (isHovered) {
                    gradient.addColorStop(0, '#34ce57');
                    gradient.addColorStop(1, '#2dd4aa');
                } else {
                    gradient.addColorStop(0, '#28a745');
                    gradient.addColorStop(1, '#20c997');
                }
            } else if (block.type === 'alloc') {
                // ALLOC区域 - 橙色
                if (isHovered) {
                    gradient.addColorStop(0, '#ff8c42');
                    gradient.addColorStop(1, '#ffc107');
                } else {
                    gradient.addColorStop(0, '#fd7e14');
                    gradient.addColorStop(1, '#e67e22');
                }
            } else {
                // gap类型 - 红色
                if (isHovered) {
                    gradient.addColorStop(0, '#e74c3c');
                    gradient.addColorStop(1, '#ff6b6b');
                } else {
                    gradient.addColorStop(0, '#dc3545');
                    gradient.addColorStop(1, '#e55353');
                }
            }

            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, width, height);

            ctx.strokeStyle = isHovered ? 'rgba(255, 255, 255, 0.8)' : 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = isHovered ? 3 : 1;
            ctx.strokeRect(x, y, width, height);
        }

        function drawFlatBlock(ctx, x, y, width, height, block, isHovered) {
            if (block.type === 'free') {
                ctx.fillStyle = isHovered ? '#2ecc71' : '#27ae60';
            } else if (block.type === 'alloc') {
                // ALLOC区域 - 橙色
                ctx.fillStyle = isHovered ? '#f39c12' : '#e67e22';
            } else {
                ctx.fillStyle = isHovered ? '#e74c3c' : '#c0392b';
            }

            ctx.fillRect(x, y, width, height);

            if (isHovered) {
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 3;
                ctx.strokeRect(x, y, width, height);
            }
        }

        function drawNeonBlock(ctx, x, y, width, height, block, isHovered) {
            // 霓虹效果背景
            ctx.fillStyle = block.type === 'free' ? '#0f3460' : '#4a0e4e';
            ctx.fillRect(x, y, width, height);

            // 霓虹边框
            ctx.strokeStyle = block.type === 'free' ? '#00ffff' : '#ff00ff';
            ctx.lineWidth = isHovered ? 4 : 2;
            ctx.shadowColor = ctx.strokeStyle;
            ctx.shadowBlur = isHovered ? 15 : 8;
            ctx.strokeRect(x, y, width, height);

            // 重置阴影
            ctx.shadowBlur = 0;
        }

        function drawClassicBlock(ctx, x, y, width, height, block, isHovered) {
            // 经典3D效果
            const baseColor = block.type === 'free' ? '#4CAF50' : '#F44336';
            const lightColor = block.type === 'free' ? '#66BB6A' : '#EF5350';
            const darkColor = block.type === 'free' ? '#388E3C' : '#D32F2F';

            // 主体
            ctx.fillStyle = isHovered ? lightColor : baseColor;
            ctx.fillRect(x, y, width, height);

            // 顶部高光
            ctx.fillStyle = lightColor;
            ctx.fillRect(x, y, width, 4);

            // 左侧高光
            ctx.fillRect(x, y, 4, height);

            // 底部阴影
            ctx.fillStyle = darkColor;
            ctx.fillRect(x, y + height - 4, width, 4);

            // 右侧阴影
            ctx.fillRect(x + width - 4, y, 4, height);
        }

        function drawGlassBlock(ctx, x, y, width, height, block, isHovered) {
            // 玻璃效果
            const alpha = isHovered ? 0.9 : 0.7;

            // 主体 - 半透明
            if (block.type === 'free') {
                ctx.fillStyle = `rgba(40, 167, 69, ${alpha})`;
            } else {
                ctx.fillStyle = `rgba(220, 53, 69, ${alpha})`;
            }
            ctx.fillRect(x, y, width, height);

            // 玻璃高光
            const highlight = ctx.createLinearGradient(x, y, x, y + height / 3);
            highlight.addColorStop(0, 'rgba(255, 255, 255, 0.6)');
            highlight.addColorStop(1, 'rgba(255, 255, 255, 0)');
            ctx.fillStyle = highlight;
            ctx.fillRect(x, y, width, height / 3);

            // 边框
            ctx.strokeStyle = isHovered ? 'rgba(255, 255, 255, 0.8)' : 'rgba(255, 255, 255, 0.4)';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, width, height);
        }

        function drawMaterialBlock(ctx, x, y, width, height, block, isHovered) {
            // Material Design风格
            const elevation = isHovered ? 8 : 2;

            // 阴影
            ctx.fillStyle = `rgba(0, 0, 0, ${0.1 + elevation * 0.02})`;
            ctx.fillRect(x + elevation, y + elevation, width, height);

            // 主体
            if (block.type === 'free') {
                ctx.fillStyle = isHovered ? '#4CAF50' : '#66BB6A';
            } else {
                ctx.fillStyle = isHovered ? '#F44336' : '#EF5350';
            }
            ctx.fillRect(x, y, width, height);

            // 涟漪效果（悬浮时）
            if (isHovered) {
                const ripple = ctx.createRadialGradient(x + width/2, y + height/2, 0, x + width/2, y + height/2, Math.max(width, height)/2);
                ripple.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
                ripple.addColorStop(1, 'rgba(255, 255, 255, 0)');
                ctx.fillStyle = ripple;
                ctx.fillRect(x, y, width, height);
            }
        }

        function drawCyberpunkBlock(ctx, x, y, width, height, block, isHovered) {
            // 赛博朋克风格
            const glowIntensity = isHovered ? 15 : 5;

            // 发光效果
            ctx.shadowColor = block.type === 'free' ? '#00ff00' : '#ff0080';
            ctx.shadowBlur = glowIntensity;

            // 主体 - 深色背景
            ctx.fillStyle = '#0a0a0a';
            ctx.fillRect(x, y, width, height);

            // 边框发光
            ctx.strokeStyle = block.type === 'free' ? '#00ff00' : '#ff0080';
            ctx.lineWidth = isHovered ? 3 : 2;
            ctx.strokeRect(x, y, width, height);

            // 内部扫描线
            ctx.shadowBlur = 0;
            ctx.strokeStyle = block.type === 'free' ? 'rgba(0, 255, 0, 0.3)' : 'rgba(255, 0, 128, 0.3)';
            ctx.lineWidth = 1;
            for (let i = y + 4; i < y + height; i += 8) {
                ctx.beginPath();
                ctx.moveTo(x + 2, i);
                ctx.lineTo(x + width - 2, i);
                ctx.stroke();
            }
        }

        function drawMinimalBlock(ctx, x, y, width, height, block, isHovered) {
            // 极简风格
            const strokeWidth = isHovered ? 3 : 1;

            // 只有边框，无填充
            ctx.strokeStyle = block.type === 'free' ? '#28a745' : '#dc3545';
            ctx.lineWidth = strokeWidth;
            ctx.strokeRect(x, y, width, height);

            // 悬浮时添加淡色填充
            if (isHovered) {
                ctx.fillStyle = block.type === 'free' ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)';
                ctx.fillRect(x, y, width, height);
            }
        }

        function drawRetroBlock(ctx, x, y, width, height, block, isHovered) {
            // 复古像素风格
            const pixelSize = 4;

            // 像素化背景
            const baseColor = block.type === 'free' ? '#00aa00' : '#aa0000';
            const lightColor = block.type === 'free' ? '#00ff00' : '#ff0000';

            ctx.fillStyle = isHovered ? lightColor : baseColor;
            ctx.fillRect(x, y, width, height);

            // 像素化边框
            ctx.fillStyle = '#ffffff';
            for (let px = x; px < x + width; px += pixelSize) {
                ctx.fillRect(px, y, pixelSize, pixelSize); // 顶边
                ctx.fillRect(px, y + height - pixelSize, pixelSize, pixelSize); // 底边
            }
            for (let py = y; py < y + height; py += pixelSize) {
                ctx.fillRect(x, py, pixelSize, pixelSize); // 左边
                ctx.fillRect(x + width - pixelSize, py, pixelSize, pixelSize); // 右边
            }

            // 像素化高光
            if (isHovered) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
                for (let px = x + pixelSize; px < x + width/2; px += pixelSize * 2) {
                    for (let py = y + pixelSize; py < y + height/2; py += pixelSize * 2) {
                        ctx.fillRect(px, py, pixelSize, pixelSize);
                    }
                }
            }
        }

        function removeHeap(heapId) {
            // 从数组中移除
            heapDataList = heapDataList.filter(heap => heap.id !== heapId);

            // 清理缩放状态
            delete canvasScales[heapId];
            delete canvasPans[heapId];

            // 从DOM中移除
            const heapDiv = document.getElementById(heapId);
            if (heapDiv) {
                heapDiv.remove();
            }

            // 如果没有堆数据了，显示添加提示
            if (heapDataList.length === 0) {
                document.getElementById('addHeapSection').style.display = 'block';
            }
        }

        function updateHeapName(heapId, newName) {
            const heap = heapDataList.find(h => h.id === heapId);
            if (heap) {
                heap.name = newName;
            }
        }

        function clearAllData() {
            heapDataList = [];
            document.getElementById('heapContainers').innerHTML = '';
            document.getElementById('dataInput').value = '';
            document.getElementById('addHeapSection').style.display = 'block';
            heapCounter = 1;
            document.getElementById('heapName').value = `堆内存 #${heapCounter}`;
        }

        function exportAllSVG() {
            if (heapDataList.length === 0) {
                alert('请先添加堆数据');
                return;
            }

            heapDataList.forEach((heapData, index) => {
                const svg = createSVG(heapData);
                const blob = new Blob([svg], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `${heapData.name}-visualization.svg`;
                a.click();

                URL.revokeObjectURL(url);
            });
        }

        function exportAllPNG() {
            if (heapDataList.length === 0) {
                alert('请先添加堆数据');
                return;
            }

            heapDataList.forEach((heapData, index) => {
                const canvas = document.getElementById(`canvas_${heapData.id}`);
                const link = document.createElement('a');
                link.download = `${heapData.name}-visualization.png`;
                link.href = canvas.toDataURL();
                link.click();
            });
        }

        function createSVG(heapData) {
            const width = 1000;
            const height = 200;
            let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;

            // Background
            svg += `<rect width="${width}" height="${height}" fill="#f8f9fa"/>`;

            // Heap blocks
            let currentX = 20;
            const blockHeight = 100;
            const blockY = 50;
            const maxWidth = width - 40;

            heapData.blocks.forEach(block => {
                const blockWidth = Math.max(1, (block.size / heapData.range.size) * maxWidth);
                const color = block.type === 'free' ? '#28a745' : '#dc3545';

                svg += `<rect x="${currentX}" y="${blockY}" width="${blockWidth}" height="${blockHeight}" fill="${color}" stroke="rgba(255,255,255,0.3)"/>`;

                if (blockWidth > 40) {
                    svg += `<text x="${currentX + blockWidth/2}" y="${blockY + blockHeight/2 + 5}" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">${block.size}B</text>`;
                }

                currentX += blockWidth;
            });

            // Address labels
            svg += `<text x="20" y="40" font-family="monospace" font-size="10" fill="#495057">0x${heapData.range.start.toString(16).toUpperCase()}</text>`;
            svg += `<text x="${width-20}" y="40" text-anchor="end" font-family="monospace" font-size="10" fill="#495057">0x${heapData.range.end.toString(16).toUpperCase()}</text>`;

            svg += '</svg>';
            return svg;
        }

        function loadExample(exampleNum) {
            const examples = {
                1: `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20002DB4,108
HEAP_FREE:0x20002E80,0x200050AC,8748
HEAP_ALLOC:0x20002DB4,0x20002E80,204
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`,
                2: `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_ALLOC:0x20002D48,0x20002D80,56
HEAP_FREE:0x20002D80,0x20002DB4,52
HEAP_ALLOC:0x20002DB4,0x20002DE8,52
HEAP_FREE:0x20002DE8,0x20002E1C,52
HEAP_ALLOC:0x20002E1C,0x20002E50,52
HEAP_FREE:0x20002E50,0x20002E84,52
HEAP_ALLOC:0x20002E84,0x20002EB8,52
HEAP_FREE:0x20002EB8,0x200050AC,8244
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`,
                3: `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_ALLOC:0x20002D48,0x20004000,4792
HEAP_FREE:0x20004000,0x20004100,256
HEAP_ALLOC:0x20004100,0x200050AC,4012
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`,
                4: `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20003000,2744
HEAP_ALLOC:0x20003000,0x20003500,1280
HEAP_FREE:0x20003500,0x20004000,2816
HEAP_ALLOC:0x20004000,0x20004800,2048
HEAP_FREE:0x20004800,0x200050AC,2220
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`
            };

            const exampleNames = {
                1: '初始状态',
                2: '内存碎片化',
                3: '大块分配',
                4: '混合使用'
            };

            if (examples[exampleNum]) {
                document.getElementById('dataInput').value = examples[exampleNum];
                document.getElementById('heapName').value = `${exampleNames[exampleNum]} #${heapCounter}`;
            }
        }

        function updateVisualizationStyle() {
            currentStyle = document.getElementById('styleSelect').value;
            // 重新绘制所有堆
            heapDataList.forEach(heapData => {
                drawHeap(heapData);
            });
        }

        function toggleExamples() {
            const content = document.getElementById('examplesContent');
            const toggle = document.getElementById('examplesToggle');

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                toggle.textContent = '▼';
            } else {
                content.classList.add('show');
                toggle.textContent = '▲';
            }
        }

        // Initialize when page loads
        window.onload = function() {
            // 页面加载时显示添加提示
            document.getElementById('addHeapSection').style.display = 'block';
        };
    </script>
</body>
</html>