{% extends "base.html" %}

{% block title %}JSON编辑器测试{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-code me-2"></i>JSON编辑器测试
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="loadSampleData()">
                            <i class="fas fa-download me-1"></i>加载示例数据
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="getSampleData()">
                            <i class="fas fa-eye me-1"></i>获取数据
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- JSON编辑器 -->
                    {% set editor_id = 'testEditor' %}
                    {% set label = '测试JSON编辑器' %}
                    {% set height = '400px' %}
                    {% set mode = 'text' %}
                    {% set read_only = false %}
                    {% set show_validation = true %}
                    {% set validation_id = 'testValidation' %}
                    {% include 'components/json_editor.html' %}
                    
                    <!-- 输出区域 -->
                    <div class="mt-4">
                        <h5>输出结果：</h5>
                        <pre id="output" class="bg-light p-3 border rounded" style="max-height: 200px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 示例数据
const sampleData = {
    "name": "测试配置",
    "version": "1.0.0",
    "settings": {
        "debug": true,
        "timeout": 30000,
        "retries": 3
    },
    "servers": [
        {
            "name": "主服务器",
            "host": "*************",
            "port": 8080,
            "ssl": true
        },
        {
            "name": "备用服务器",
            "host": "*************",
            "port": 8080,
            "ssl": false
        }
    ],
    "features": {
        "authentication": true,
        "logging": true,
        "monitoring": false
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待编辑器初始化
    setTimeout(() => {
        loadSampleData();
    }, 300);
});

// 加载示例数据
function loadSampleData() {
    const editor = window.jsonEditors['testEditor'];
    if (editor) {
        editor.setContent(sampleData);
        updateOutput('示例数据已加载');
    } else {
        updateOutput('编辑器未初始化');
    }
}

// 获取数据
function getSampleData() {
    const editor = window.jsonEditors['testEditor'];
    if (editor) {
        try {
            const jsonData = editor.getJSON();
            const textData = editor.getText();
            
            updateOutput(`JSON对象:\n${JSON.stringify(jsonData, null, 2)}\n\n文本内容:\n${textData}`);
        } catch (error) {
            updateOutput(`获取数据失败: ${error.message}`);
        }
    } else {
        updateOutput('编辑器未初始化');
    }
}

// 更新输出
function updateOutput(content) {
    const output = document.getElementById('output');
    output.textContent = content;
}

// 重写通知函数以适配测试页面
window.showNotification = function(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
};
</script>
{% endblock %}
