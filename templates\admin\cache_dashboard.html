{% extends "base.html" %}

{% block title %}缓存管理 - 充电桩管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-memory text-primary me-2"></i>缓存管理
                    </h3>
                </div>
                <div class="card-body">
                    <!-- 缓存统计 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="hitCount">-</h4>
                                            <p class="mb-0">缓存命中</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-bullseye fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="missCount">-</h4>
                                            <p class="mb-0">缓存未命中</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-times-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="totalRequests">-</h4>
                                            <p class="mb-0">总请求数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-chart-bar fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="hitRate">-</h4>
                                            <p class="mb-0">命中率</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-percentage fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 缓存操作 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">缓存操作</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="refreshStats()">
                                            <i class="fas fa-sync-alt me-1"></i>刷新统计
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" onclick="clearCache('device')">
                                            <i class="fas fa-trash me-1"></i>清除设备缓存
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="clearCache('ota')">
                                            <i class="fas fa-trash me-1"></i>清除OTA缓存
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="clearCache('firmware')">
                                            <i class="fas fa-trash me-1"></i>清除固件缓存
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="clearCache('all')">
                                            <i class="fas fa-trash-alt me-1"></i>清除所有缓存
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 缓存信息 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">缓存配置信息</h5>
                                </div>
                                <div class="card-body">
                                    <div id="cacheInfo">
                                        <div class="text-center">
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 通知容器 -->
<div id="notificationContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    refreshStats();
    loadCacheInfo();
    
    // 每30秒自动刷新统计
    setInterval(refreshStats, 30000);
});

// 刷新缓存统计
function refreshStats() {
    fetch('/api/cache/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.stats;
                document.getElementById('hitCount').textContent = stats.hit_count;
                document.getElementById('missCount').textContent = stats.miss_count;
                document.getElementById('totalRequests').textContent = stats.total_requests;
                document.getElementById('hitRate').textContent = stats.hit_rate + '%';
            } else {
                showNotification('获取缓存统计失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('获取缓存统计失败:', error);
            showNotification('获取缓存统计失败', 'error');
        });
}

// 清除缓存
function clearCache(type) {
    const typeNames = {
        'all': '所有缓存',
        'device': '设备缓存',
        'ota': 'OTA缓存',
        'firmware': '固件缓存'
    };
    
    if (!confirm(`确定要清除${typeNames[type]}吗？`)) {
        return;
    }
    
    fetch('/api/cache/clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type: type })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            refreshStats(); // 刷新统计
        } else {
            showNotification('清除缓存失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('清除缓存失败:', error);
        showNotification('清除缓存失败', 'error');
    });
}

// 加载缓存信息
function loadCacheInfo() {
    fetch('/api/cache/info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const info = data.info;
                const html = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>缓存类型</h6>
                            <p class="text-muted">${info.cache_type}</p>
                            
                            <h6>描述</h6>
                            <p class="text-muted">${info.description}</p>
                            
                            <h6>特性</h6>
                            <ul class="text-muted">
                                ${info.features.map(feature => `<li>${feature}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>限制</h6>
                            <ul class="text-warning">
                                ${info.limitations.map(limitation => `<li>${limitation}</li>`).join('')}
                            </ul>
                            
                            <h6>建议</h6>
                            <ul class="text-info">
                                ${info.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `;
                document.getElementById('cacheInfo').innerHTML = html;
            } else {
                document.getElementById('cacheInfo').innerHTML = 
                    '<div class="alert alert-danger">加载缓存信息失败: ' + data.error + '</div>';
            }
        })
        .catch(error => {
            console.error('加载缓存信息失败:', error);
            document.getElementById('cacheInfo').innerHTML = 
                '<div class="alert alert-danger">加载缓存信息失败</div>';
        });
}

// 显示通知
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show`;
    notification.innerHTML = `
        <i class="fas ${iconClass} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.getElementById('notificationContainer').appendChild(notification);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
{% endblock %}
