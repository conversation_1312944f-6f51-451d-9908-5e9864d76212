<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keil Microlib 堆内存分析工具 V2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .input-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .input-area {
            width: 100%;
            height: 150px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            resize: vertical;
            transition: border-color 0.3s;
        }

        .input-area:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .display-modes {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .mode-btn {
            padding: 8px 16px;
            border: 2px solid #dee2e6;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
        }

        .mode-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .mode-btn:hover {
            border-color: #667eea;
        }

        .visualization-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .heap-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .info-item {
            text-align: center;
        }

        .info-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        .memory-container {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            background: #fafafa;
            min-height: 200px;
            position: relative;
        }

        .memory-blocks {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            align-items: flex-start;
        }

        .memory-block {
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            position: relative;
            min-height: 30px;
        }

        .memory-block:hover {
            transform: scale(1.05);
            z-index: 10;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .legend {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            max-width: 300px;
            line-height: 1.4;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            display: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Keil Microlib 堆内存分析工具 V2</h1>
            <p>专业的内存碎片可视化分析工具</p>
        </div>

        <div class="input-section">
            <h3>📝 输入堆内存数据</h3>
            <textarea class="input-area" id="heapInput" placeholder="请粘贴Keil Microlib堆内存分析数据...

示例格式:
HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20002D54,12
HEAP_ALLOC:0x20002D54,0x20002D58,4
HEAP_FREE:0x20002D58,0x20002D74,28
HEAP_ALLOC:0x20002D74,0x20002D78,4
HEAP_FREE:0x20002D78,0x200050AC,9012
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END"></textarea>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="analyzeHeap()">🔍 分析内存</button>
                <button class="btn btn-secondary" onclick="clearData()">🗑️ 清除数据</button>
                
                <div class="display-modes">
                    <span style="font-weight: 600; color: #495057;">显示模式:</span>
                    <button class="mode-btn active" data-mode="proportional" onclick="setDisplayMode('proportional')">比例模式</button>
                    <button class="mode-btn" data-mode="uniform" onclick="setDisplayMode('uniform')">统一模式</button>
                    <button class="mode-btn" data-mode="intensity" onclick="setDisplayMode('intensity')">深浅模式</button>
                </div>
            </div>
            
            <div id="message"></div>
        </div>

        <div class="visualization-section" id="visualizationSection" style="display: none;">
            <h3>📊 内存布局可视化</h3>
            
            <div class="heap-info" id="heapInfo"></div>
            
            <div class="memory-container">
                <div class="memory-blocks" id="memoryBlocks"></div>
            </div>
            
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #28a745, #20c997);"></div>
                    <span>空闲区域 (FREE)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #fd7e14, #e67e22);"></div>
                    <span>已分配区域 (ALLOC)</span>
                </div>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        let heapData = null;
        let currentDisplayMode = 'proportional';

        function parseHeapData(text) {
            const lines = text.trim().split('\n');
            const data = { range: null, blocks: [] };

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine || trimmedLine.startsWith('HEAP_END')) continue;

                if (trimmedLine.startsWith('HEAP_RANGE:')) {
                    const parts = trimmedLine.substring(11).split(',');
                    data.range = {
                        start: parseInt(parts[0], 16),
                        end: parseInt(parts[1], 16),
                        size: parseInt(parts[2])
                    };
                } else if (trimmedLine.startsWith('HEAP_FREE:')) {
                    const parts = trimmedLine.substring(10).split(',');
                    const start = parseInt(parts[0], 16);
                    const end = parseInt(parts[1], 16);
                    const size = parseInt(parts[2]);

                    if (!isNaN(start) && !isNaN(end) && !isNaN(size) && start < end) {
                        data.blocks.push({
                            type: 'free',
                            start: start,
                            end: end,
                            size: size
                        });
                    }
                } else if (trimmedLine.startsWith('HEAP_ALLOC:')) {
                    const parts = trimmedLine.substring(11).split(',');
                    const start = parseInt(parts[0], 16);
                    const end = parseInt(parts[1], 16);
                    const size = parseInt(parts[2]);

                    if (!isNaN(start) && !isNaN(end) && !isNaN(size) && start < end) {
                        data.blocks.push({
                            type: 'alloc',
                            start: start,
                            end: end,
                            size: size
                        });
                    }
                }
            }

            // 按地址排序
            data.blocks.sort((a, b) => a.start - b.start);

            return data;
        }

        function analyzeHeap() {
            const input = document.getElementById('heapInput').value.trim();
            const messageDiv = document.getElementById('message');

            if (!input) {
                showMessage('请输入堆内存数据', 'error');
                return;
            }

            try {
                heapData = parseHeapData(input);

                if (!heapData.range) {
                    showMessage('未找到HEAP_RANGE数据，请检查输入格式', 'error');
                    return;
                }

                if (heapData.blocks.length === 0) {
                    showMessage('未找到内存块数据，请检查输入格式', 'error');
                    return;
                }

                showMessage(`成功解析 ${heapData.blocks.length} 个内存块`, 'success');
                displayHeapInfo();
                renderMemoryBlocks();
                document.getElementById('visualizationSection').style.display = 'block';

            } catch (error) {
                showMessage('数据解析失败: ' + error.message, 'error');
            }
        }

        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
            messageDiv.textContent = text;
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 3000);
        }

        function displayHeapInfo() {
            if (!heapData) return;

            const totalFree = heapData.blocks.filter(b => b.type === 'free').reduce((sum, b) => sum + b.size, 0);
            const totalAlloc = heapData.blocks.filter(b => b.type === 'alloc').reduce((sum, b) => sum + b.size, 0);
            const usagePercent = ((totalAlloc / heapData.range.size) * 100).toFixed(1);
            const fragmentCount = heapData.blocks.filter(b => b.type === 'free').length;

            document.getElementById('heapInfo').innerHTML = `
                <div class="info-item">
                    <div class="info-label">堆起始地址</div>
                    <div class="info-value">0x${heapData.range.start.toString(16).toUpperCase()}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">堆大小</div>
                    <div class="info-value">${heapData.range.size.toLocaleString()} 字节</div>
                </div>
                <div class="info-item">
                    <div class="info-label">已使用</div>
                    <div class="info-value">${totalAlloc.toLocaleString()} 字节 (${usagePercent}%)</div>
                </div>
                <div class="info-item">
                    <div class="info-label">空闲</div>
                    <div class="info-value">${totalFree.toLocaleString()} 字节</div>
                </div>
                <div class="info-item">
                    <div class="info-label">内存块数量</div>
                    <div class="info-value">${heapData.blocks.length} 个</div>
                </div>
                <div class="info-item">
                    <div class="info-label">空闲碎片</div>
                    <div class="info-value">${fragmentCount} 个</div>
                </div>
            `;
        }

        function renderMemoryBlocks() {
            if (!heapData) return;

            const container = document.getElementById('memoryBlocks');
            container.innerHTML = '';

            switch (currentDisplayMode) {
                case 'proportional':
                    renderProportionalMode(container);
                    break;
                case 'uniform':
                    renderUniformMode(container);
                    break;
                case 'intensity':
                    renderIntensityMode(container);
                    break;
            }
        }

        function renderProportionalMode(container) {
            // 比例模式：块的显示大小与实际大小成比例
            const maxSize = Math.max(...heapData.blocks.map(b => b.size));
            const minWidth = 20; // 最小宽度，确保小块可见
            const maxWidth = 300; // 最大宽度

            heapData.blocks.forEach((block, index) => {
                const blockDiv = document.createElement('div');
                blockDiv.className = 'memory-block';

                // 计算宽度（对数缩放，让小块也能看见）
                const sizeRatio = Math.log(block.size + 1) / Math.log(maxSize + 1);
                const width = minWidth + (maxWidth - minWidth) * sizeRatio;

                blockDiv.style.width = width + 'px';
                blockDiv.style.height = '40px';
                blockDiv.style.background = getBlockColor(block.type, false);
                blockDiv.style.marginBottom = '5px';

                // 显示大小信息
                if (width > 60) {
                    blockDiv.textContent = formatSize(block.size);
                } else if (width > 30) {
                    blockDiv.textContent = block.size + 'B';
                }

                setupBlockEvents(blockDiv, block, index);
                container.appendChild(blockDiv);
            });
        }

        function renderUniformMode(container) {
            // 统一模式：所有块显示大小相同
            const blockWidth = 80;
            const blockHeight = 40;

            heapData.blocks.forEach((block, index) => {
                const blockDiv = document.createElement('div');
                blockDiv.className = 'memory-block';

                blockDiv.style.width = blockWidth + 'px';
                blockDiv.style.height = blockHeight + 'px';
                blockDiv.style.background = getBlockColor(block.type, false);
                blockDiv.style.marginBottom = '5px';

                blockDiv.textContent = formatSize(block.size);

                setupBlockEvents(blockDiv, block, index);
                container.appendChild(blockDiv);
            });
        }

        function renderIntensityMode(container) {
            // 深浅模式：通过颜色深浅表示大小
            const maxSize = Math.max(...heapData.blocks.map(b => b.size));
            const blockWidth = 80;
            const blockHeight = 40;

            heapData.blocks.forEach((block, index) => {
                const blockDiv = document.createElement('div');
                blockDiv.className = 'memory-block';

                blockDiv.style.width = blockWidth + 'px';
                blockDiv.style.height = blockHeight + 'px';

                // 根据大小计算颜色深浅
                const intensity = Math.log(block.size + 1) / Math.log(maxSize + 1);
                blockDiv.style.background = getBlockColor(block.type, false, intensity);
                blockDiv.style.marginBottom = '5px';

                blockDiv.textContent = formatSize(block.size);

                setupBlockEvents(blockDiv, block, index);
                container.appendChild(blockDiv);
            });
        }

        function getBlockColor(type, isHovered, intensity = 1) {
            if (type === 'free') {
                if (currentDisplayMode === 'intensity') {
                    const alpha = 0.3 + 0.7 * intensity; // 30% 到 100% 的透明度
                    return `rgba(40, 167, 69, ${alpha})`;
                }
                return isHovered ?
                    'linear-gradient(135deg, #34ce57, #2dd4aa)' :
                    'linear-gradient(135deg, #28a745, #20c997)';
            } else {
                if (currentDisplayMode === 'intensity') {
                    const alpha = 0.3 + 0.7 * intensity;
                    return `rgba(253, 126, 20, ${alpha})`;
                }
                return isHovered ?
                    'linear-gradient(135deg, #ff8c42, #ffc107)' :
                    'linear-gradient(135deg, #fd7e14, #e67e22)';
            }
        }

        function formatSize(bytes) {
            if (bytes >= 1024) {
                return (bytes / 1024).toFixed(1) + 'KB';
            }
            return bytes + 'B';
        }

        function setupBlockEvents(blockDiv, block, index) {
            blockDiv.addEventListener('mouseenter', (e) => {
                blockDiv.style.background = getBlockColor(block.type, true);
                showTooltip(e, block, index);
            });

            blockDiv.addEventListener('mouseleave', () => {
                blockDiv.style.background = getBlockColor(block.type, false);
                hideTooltip();
            });

            blockDiv.addEventListener('mousemove', (e) => {
                updateTooltipPosition(e);
            });
        }

        function showTooltip(e, block, index) {
            const tooltip = document.getElementById('tooltip');
            const utilization = ((block.size / heapData.range.size) * 100).toFixed(2);
            const typeIcon = block.type === 'free' ? '🟢' : '🟧';
            const typeName = block.type === 'free' ? '空闲区域' : '已分配区域';

            tooltip.innerHTML = `
                <div style="border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 8px; margin-bottom: 8px;">
                    <strong>${typeIcon} ${typeName} #${index + 1}</strong>
                </div>
                <div style="display: grid; grid-template-columns: auto 1fr; gap: 8px; font-size: 11px;">
                    <span>起始地址:</span><span style="font-family: monospace;">0x${block.start.toString(16).toUpperCase()}</span>
                    <span>结束地址:</span><span style="font-family: monospace;">0x${block.end.toString(16).toUpperCase()}</span>
                    <span>大小:</span><span>${block.size.toLocaleString()} 字节 (${formatSize(block.size)})</span>
                    <span>占用比例:</span><span>${utilization}%</span>
                </div>
            `;

            tooltip.style.display = 'block';
            updateTooltipPosition(e);
        }

        function updateTooltipPosition(e) {
            const tooltip = document.getElementById('tooltip');
            tooltip.style.left = e.clientX + 15 + 'px';
            tooltip.style.top = e.clientY - 10 + 'px';
        }

        function hideTooltip() {
            document.getElementById('tooltip').style.display = 'none';
        }

        function setDisplayMode(mode) {
            currentDisplayMode = mode;

            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

            // 重新渲染
            if (heapData) {
                renderMemoryBlocks();
            }
        }

        function clearData() {
            document.getElementById('heapInput').value = '';
            document.getElementById('visualizationSection').style.display = 'none';
            document.getElementById('message').style.display = 'none';
            heapData = null;
        }

        // 页面加载时设置示例数据
        window.addEventListener('load', () => {
            const exampleData = `HEAP_RANGE:0x20002D48,0x200050B0,9064
HEAP_FREE:0x20002D48,0x20002D54,12
HEAP_ALLOC:0x20002D54,0x20002D58,4
HEAP_FREE:0x20002D58,0x20002D74,28
HEAP_ALLOC:0x20002D74,0x20002D78,4
HEAP_FREE:0x20002D78,0x200050AC,9012
HEAP_ALLOC:0x200050AC,0x200050B0,4
HEAP_END`;

            document.getElementById('heapInput').value = exampleData;
        });
    </script>
</body>
</html>
