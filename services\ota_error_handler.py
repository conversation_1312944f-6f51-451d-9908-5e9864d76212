#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA错误处理和重试机制
提供详细的错误分类和智能重试策略
"""

import time
import random
from typing import Dict, <PERSON><PERSON>, Optional
from enum import Enum
from utils.logger import LoggerManager
from services.ota_task_state import OtaErrorType, OtaTaskDetailedStatus

# 获取日志记录器
logger = LoggerManager.get_logger()


class RetryStrategy(Enum):
    """重试策略枚举"""
    NO_RETRY = "no_retry"
    IMMEDIATE = "immediate"
    LINEAR_BACKOFF = "linear_backoff"
    EXPONENTIAL_BACKOFF = "exponential_backoff"


class OtaErrorHandler:
    """OTA错误处理器"""

    # 错误代码到错误类型的映射
    ERROR_CODE_MAPPING = {
        # 网络错误
        "CONNECTION_TIMEOUT": OtaErrorType.NETWORK_ERROR,
        "CONNECTION_REFUSED": OtaErrorType.NETWORK_ERROR,
        "NETWORK_UNREACHABLE": OtaErrorType.NETWORK_ERROR,
        "DNS_RESOLUTION_FAILED": OtaErrorType.NETWORK_ERROR,

        # 设备错误
        "DEVICE_NOT_RESPONDING": OtaErrorType.DEVICE_ERROR,
        "DEVICE_BUSY": OtaErrorType.DEVICE_ERROR,
        "DEVICE_OFFLINE": OtaErrorType.DEVICE_ERROR,
        "INVALID_DEVICE_RESPONSE": OtaErrorType.DEVICE_ERROR,
        "DEVICE_MEMORY_FULL": OtaErrorType.DEVICE_ERROR,

        # 固件错误
        "FIRMWARE_NOT_FOUND": OtaErrorType.FIRMWARE_ERROR,
        "FIRMWARE_CORRUPTED": OtaErrorType.FIRMWARE_ERROR,
        "FIRMWARE_INCOMPATIBLE": OtaErrorType.FIRMWARE_ERROR,
        "FIRMWARE_TOO_LARGE": OtaErrorType.FIRMWARE_ERROR,
        "INVALID_FIRMWARE_FORMAT": OtaErrorType.FIRMWARE_ERROR,

        # 系统错误
        "INSUFFICIENT_MEMORY": OtaErrorType.SYSTEM_ERROR,
        "DISK_FULL": OtaErrorType.SYSTEM_ERROR,
        "PERMISSION_DENIED": OtaErrorType.SYSTEM_ERROR,
        "SERVICE_UNAVAILABLE": OtaErrorType.SYSTEM_ERROR,

        # 超时错误
        "OTA_TIMEOUT": OtaErrorType.TIMEOUT_ERROR,
        "UPLOAD_TIMEOUT": OtaErrorType.TIMEOUT_ERROR,
        "RESPONSE_TIMEOUT": OtaErrorType.TIMEOUT_ERROR,

        # 验证错误
        "CHECKSUM_MISMATCH": OtaErrorType.VALIDATION_ERROR,
        "VERSION_MISMATCH": OtaErrorType.VALIDATION_ERROR,
        "SIGNATURE_INVALID": OtaErrorType.VALIDATION_ERROR,
    }

    # 错误类型到重试策略的映射
    RETRY_STRATEGIES = {
        OtaErrorType.NETWORK_ERROR: RetryStrategy.EXPONENTIAL_BACKOFF,
        OtaErrorType.DEVICE_ERROR: RetryStrategy.LINEAR_BACKOFF,
        OtaErrorType.FIRMWARE_ERROR: RetryStrategy.NO_RETRY,
        OtaErrorType.SYSTEM_ERROR: RetryStrategy.LINEAR_BACKOFF,
        OtaErrorType.TIMEOUT_ERROR: RetryStrategy.EXPONENTIAL_BACKOFF,
        OtaErrorType.VALIDATION_ERROR: RetryStrategy.NO_RETRY,
    }

    # 最大重试次数配置
    MAX_RETRIES = {
        OtaErrorType.NETWORK_ERROR: 3,
        OtaErrorType.DEVICE_ERROR: 2,
        OtaErrorType.FIRMWARE_ERROR: 0,
        OtaErrorType.SYSTEM_ERROR: 2,
        OtaErrorType.TIMEOUT_ERROR: 3,
        OtaErrorType.VALIDATION_ERROR: 0,
    }

    def __init__(self):
        self.retry_delays = {}  # 记录每个任务的重试延迟

    def classify_error(self, error_message: str, stage: OtaTaskDetailedStatus) -> Tuple[OtaErrorType, str]:
        """
        根据错误消息和阶段分类错误

        Args:
            error_message: 错误消息
            stage: 当前阶段

        Returns:
            (错误类型, 错误代码)
        """
        error_message_lower = error_message.lower()

        # 根据关键词匹配错误类型
        if any(keyword in error_message_lower for keyword in ["timeout", "超时"]):
            if stage in [OtaTaskDetailedStatus.CONNECTING]:
                return OtaErrorType.NETWORK_ERROR, "CONNECTION_TIMEOUT"
            elif stage in [OtaTaskDetailedStatus.SENDING_SLICES]:
                return OtaErrorType.TIMEOUT_ERROR, "UPLOAD_TIMEOUT"
            else:
                return OtaErrorType.TIMEOUT_ERROR, "OTA_TIMEOUT"

        elif any(keyword in error_message_lower for keyword in ["connection", "连接", "网络"]):
            if "refused" in error_message_lower or "拒绝" in error_message_lower:
                return OtaErrorType.NETWORK_ERROR, "CONNECTION_REFUSED"
            elif "unreachable" in error_message_lower or "不可达" in error_message_lower:
                return OtaErrorType.NETWORK_ERROR, "NETWORK_UNREACHABLE"
            else:
                return OtaErrorType.NETWORK_ERROR, "CONNECTION_TIMEOUT"

        elif any(keyword in error_message_lower for keyword in ["device", "设备"]):
            if "not responding" in error_message_lower or "无响应" in error_message_lower:
                return OtaErrorType.DEVICE_ERROR, "DEVICE_NOT_RESPONDING"
            elif "busy" in error_message_lower or "忙碌" in error_message_lower:
                return OtaErrorType.DEVICE_ERROR, "DEVICE_BUSY"
            elif "offline" in error_message_lower or "离线" in error_message_lower:
                return OtaErrorType.DEVICE_ERROR, "DEVICE_OFFLINE"
            else:
                return OtaErrorType.DEVICE_ERROR, "INVALID_DEVICE_RESPONSE"

        elif any(keyword in error_message_lower for keyword in ["firmware", "固件"]):
            if "not found" in error_message_lower or "不存在" in error_message_lower:
                return OtaErrorType.FIRMWARE_ERROR, "FIRMWARE_NOT_FOUND"
            elif "corrupted" in error_message_lower or "损坏" in error_message_lower:
                return OtaErrorType.FIRMWARE_ERROR, "FIRMWARE_CORRUPTED"
            elif "incompatible" in error_message_lower or "不兼容" in error_message_lower:
                return OtaErrorType.FIRMWARE_ERROR, "FIRMWARE_INCOMPATIBLE"
            else:
                return OtaErrorType.FIRMWARE_ERROR, "INVALID_FIRMWARE_FORMAT"

        elif any(keyword in error_message_lower for keyword in ["memory", "内存", "disk", "磁盘"]):
            if "full" in error_message_lower or "满" in error_message_lower:
                return OtaErrorType.SYSTEM_ERROR, "DISK_FULL"
            else:
                return OtaErrorType.SYSTEM_ERROR, "INSUFFICIENT_MEMORY"

        elif any(keyword in error_message_lower for keyword in ["checksum", "校验", "signature", "签名"]):
            if "checksum" in error_message_lower or "校验" in error_message_lower:
                return OtaErrorType.VALIDATION_ERROR, "CHECKSUM_MISMATCH"
            else:
                return OtaErrorType.VALIDATION_ERROR, "SIGNATURE_INVALID"

        # 默认分类为系统错误
        return OtaErrorType.SYSTEM_ERROR, "SERVICE_UNAVAILABLE"

    def should_retry(self, error_type: OtaErrorType, retry_count: int) -> bool:
        """
        判断是否应该重试

        Args:
            error_type: 错误类型
            retry_count: 当前重试次数

        Returns:
            是否应该重试
        """
        max_retries = self.MAX_RETRIES.get(error_type, 0)
        return retry_count < max_retries

    def calculate_retry_delay(self, task_id: int, error_type: OtaErrorType,
                            retry_count: int) -> float:
        """
        计算重试延迟时间

        Args:
            task_id: 任务ID
            error_type: 错误类型
            retry_count: 重试次数

        Returns:
            延迟时间（秒）
        """
        strategy = self.RETRY_STRATEGIES.get(error_type, RetryStrategy.NO_RETRY)

        if strategy == RetryStrategy.NO_RETRY:
            return 0

        elif strategy == RetryStrategy.IMMEDIATE:
            return 0

        elif strategy == RetryStrategy.LINEAR_BACKOFF:
            # 线性退避：1秒、2秒、3秒...
            return retry_count * 1.0

        elif strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            # 指数退避：1秒、2秒、4秒、8秒...，加上随机抖动
            base_delay = min(2 ** retry_count, 60)  # 最大60秒
            jitter = random.uniform(0.1, 0.5)  # 10%-50%的随机抖动
            return base_delay * (1 + jitter)

        return 0

    def get_user_friendly_message(self, error_type: OtaErrorType,
                                 error_code: str) -> str:
        """
        获取用户友好的错误消息

        Args:
            error_type: 错误类型
            error_code: 错误代码

        Returns:
            用户友好的错误消息
        """
        messages = {
            # 网络错误
            "CONNECTION_TIMEOUT": "连接设备超时，请检查网络连接",
            "CONNECTION_REFUSED": "设备拒绝连接，请检查设备状态",
            "NETWORK_UNREACHABLE": "网络不可达，请检查网络配置",
            "DNS_RESOLUTION_FAILED": "域名解析失败，请检查DNS配置",

            # 设备错误
            "DEVICE_NOT_RESPONDING": "设备无响应，请检查设备是否在线",
            "DEVICE_BUSY": "设备忙碌，请稍后重试",
            "DEVICE_OFFLINE": "设备离线，请确保设备已连接",
            "INVALID_DEVICE_RESPONSE": "设备响应异常，请检查设备状态",
            "DEVICE_MEMORY_FULL": "设备内存不足，请清理设备存储空间",

            # 固件错误
            "FIRMWARE_NOT_FOUND": "固件文件不存在，请检查固件路径",
            "FIRMWARE_CORRUPTED": "固件文件损坏，请重新上传固件",
            "FIRMWARE_INCOMPATIBLE": "固件版本不兼容，请选择正确的固件版本",
            "FIRMWARE_TOO_LARGE": "固件文件过大，请检查固件大小",
            "INVALID_FIRMWARE_FORMAT": "固件格式无效，请检查固件文件",

            # 系统错误
            "INSUFFICIENT_MEMORY": "系统内存不足，请稍后重试",
            "DISK_FULL": "磁盘空间不足，请清理存储空间",
            "PERMISSION_DENIED": "权限不足，请检查文件权限",
            "SERVICE_UNAVAILABLE": "服务暂时不可用，请稍后重试",

            # 超时错误
            "OTA_TIMEOUT": "OTA升级超时，请检查网络连接和设备状态",
            "UPLOAD_TIMEOUT": "固件上传超时，请检查网络连接",
            "RESPONSE_TIMEOUT": "等待设备响应超时，请检查设备状态",

            # 验证错误
            "CHECKSUM_MISMATCH": "固件校验失败，请重新上传固件",
            "VERSION_MISMATCH": "版本不匹配，请检查固件版本",
            "SIGNATURE_INVALID": "固件签名无效，请使用正确的固件文件",
        }

        return messages.get(error_code, f"未知错误: {error_code}")


# 创建全局错误处理器实例
ota_error_handler = OtaErrorHandler()
