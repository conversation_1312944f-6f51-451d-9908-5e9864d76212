#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA设备管理后台
用于管理所有设备并进行OTA升级
"""

import sys
import argparse
from utils.logger import LoggerManager
from utils.socket_manager import run_socketio
from app_factory import create_app, init_db

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建应用
app = create_app()

# 启动应用
if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="OTA设备管理系统")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址 (默认: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=5000, help="服务器端口 (默认: 5001)")
    parser.add_argument("--debug", action="store_true", default=False, help="调试模式 (默认: False)")
    parser.add_argument("--no-debug", dest="debug", action="store_false", help="关闭调试模式")

    args = parser.parse_args()

    try:
        # 初始化数据库
        init_db(app)
        logger.info("数据库初始化完成")

        # 输出启动信息
        logger.info(f"启动服务器: {args.host}:{args.port}, 调试模式: {args.debug}")

        # 启动应用
        run_socketio(app, host=args.host, port=args.port, debug=args.debug)
    except Exception as e:
        logger.error(f"应用程序启动失败: {str(e)}")
        sys.exit(1)
