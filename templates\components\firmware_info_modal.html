<!-- 固件信息查询模态框 -->
<div class="modal fade" id="firmwareInfoModal" tabindex="-1" aria-labelledby="firmwareInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="firmwareInfoModalLabel">设备固件信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="firmwareInfoLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备固件信息...</p>
                </div>
                <div id="firmwareInfoContent" class="d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">固件1 (FW1) 信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>固件大小:</th>
                                                <td id="fw1_size">--</td>
                                            </tr>
                                            <tr>
                                                <th>CRC32校验:</th>
                                                <td id="fw1_crc32">--</td>
                                            </tr>
                                            <tr>
                                                <th>更新时间:</th>
                                                <td id="fw1_update_time">--</td>
                                            </tr>
                                            <tr>
                                                <th>分区容量:</th>
                                                <td id="fw1_capacity">--</td>
                                            </tr>
                                            <tr>
                                                <th>固件版本:</th>
                                                <td id="fw1_version">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">固件2 (FW2) 信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>固件大小:</th>
                                                <td id="fw2_size">--</td>
                                            </tr>
                                            <tr>
                                                <th>CRC32校验:</th>
                                                <td id="fw2_crc32">--</td>
                                            </tr>
                                            <tr>
                                                <th>更新时间:</th>
                                                <td id="fw2_update_time">--</td>
                                            </tr>
                                            <tr>
                                                <th>分区容量:</th>
                                                <td id="fw2_capacity">--</td>
                                            </tr>
                                            <tr>
                                                <th>固件版本:</th>
                                                <td id="fw2_version">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">系统信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>Bootloader版本:</th>
                                                <td id="bootloader_version">--</td>
                                            </tr>
                                            <tr>
                                                <th>设备类型:</th>
                                                <td id="device_type">--</td>
                                            </tr>
                                            <tr>
                                                <th>设备功能:</th>
                                                <td id="device_func">--</td>
                                            </tr>
                                            <tr>
                                                <th>编译时间:</th>
                                                <td id="compile_ts">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">设备唯一标识 (UID)</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>UID[0]:</th>
                                                <td id="uid_0">--</td>
                                            </tr>
                                            <tr>
                                                <th>UID[1]:</th>
                                                <td id="uid_1">--</td>
                                            </tr>
                                            <tr>
                                                <th>UID[2]:</th>
                                                <td id="uid_2">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="firmwareInfoError" class="alert alert-danger d-none">
                    获取设备固件信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryFirmwareInfo(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<script>
// 查询设备固件信息
function queryFirmwareInfo(isRefresh = false) {
    // 显示模态框
    if (!isRefresh) {
        const modal = new bootstrap.Modal(document.getElementById('firmwareInfoModal'));
        modal.show();
    }

    // 显示加载中状态
    document.getElementById('firmwareInfoLoading').classList.remove('d-none');
    document.getElementById('firmwareInfoContent').classList.add('d-none');
    document.getElementById('firmwareInfoError').classList.add('d-none');

    // 发送请求获取设备固件信息
    fetch(`/api/device/${window.deviceId}/firmware_info`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            // 隐藏加载中状态
            document.getElementById('firmwareInfoLoading').classList.add('d-none');

            if (data.error) {
                // 显示错误信息
                document.getElementById('firmwareInfoError').textContent = '获取设备固件信息失败: ' + data.error;
                document.getElementById('firmwareInfoError').classList.remove('d-none');
                return;
            }

            // 解析并显示固件信息
            displayFirmwareInfo(data);

            // 显示内容区域
            document.getElementById('firmwareInfoContent').classList.remove('d-none');
        })
        .catch(error => {
            console.error('获取设备固件信息失败:', error);
            // 隐藏加载中状态，显示错误信息
            document.getElementById('firmwareInfoLoading').classList.add('d-none');
            document.getElementById('firmwareInfoError').textContent = '获取设备固件信息失败: ' + error.message;
            document.getElementById('firmwareInfoError').classList.remove('d-none');
        });
}

// 显示固件信息
function displayFirmwareInfo(data) {
    // 检查是否有固件信息
    if (!data) {
        document.getElementById('firmwareInfoError').textContent = '设备返回的固件信息格式不正确';
        document.getElementById('firmwareInfoError').classList.remove('d-none');
        return;
    }

    // 根据实际返回的数据结构进行处理
    // 数据格式: {'session_id': 1, 'result': 0, 'info': {...}}
    if (data.result !== 0) {
        document.getElementById('firmwareInfoError').textContent = '设备返回错误: ' + data.result;
        document.getElementById('firmwareInfoError').classList.remove('d-none');
        return;
    }

    if (!data.info) {
        document.getElementById('firmwareInfoError').textContent = '设备返回的固件信息不包含info字段';
        document.getElementById('firmwareInfoError').classList.remove('d-none');
        return;
    }

    const info = data.info;

    // 格式化函数
    function formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function formatHex(value) {
        return '0x' + value.toString(16).toUpperCase().padStart(8, '0');
    }

    function formatTimestamp(timestamp) {
        if (timestamp === 0) return '未设置';
        return new Date(timestamp * 1000).toLocaleString();
    }

    function formatVersion(version) {
        // 假设版本格式为 0xAABBCCDD，其中 AA.BB.CC.DD
        const major = (version >> 24) & 0xFF;
        const minor = (version >> 16) & 0xFF;
        const patch = (version >> 8) & 0xFF;
        const build = version & 0xFF;
        return `${major}.${minor}.${patch}.${build}`;
    }

    // 更新FW1信息
    document.getElementById('fw1_size').textContent = formatSize(info.fw1_size || 0);
    document.getElementById('fw1_crc32').textContent = formatHex(info.fw1_crc32 || 0);
    document.getElementById('fw1_update_time').textContent = formatTimestamp(info.fw1_update_time || 0);
    document.getElementById('fw1_capacity').textContent = formatSize(info.fw1_capacity || 0);
    document.getElementById('fw1_version').textContent = formatVersion(info.fw1_version || 0);

    // 更新FW2信息
    document.getElementById('fw2_size').textContent = formatSize(info.fw2_size || 0);
    document.getElementById('fw2_crc32').textContent = formatHex(info.fw2_crc32 || 0);
    document.getElementById('fw2_update_time').textContent = formatTimestamp(info.fw2_update_time || 0);
    document.getElementById('fw2_capacity').textContent = formatSize(info.fw2_capacity || 0);
    document.getElementById('fw2_version').textContent = formatVersion(info.fw2_version || 0);

    // 更新系统信息
    document.getElementById('bootloader_version').textContent = formatVersion(info.bootloader_version || 0);
    document.getElementById('device_type').textContent = info.device_type_name || '未知';
    document.getElementById('device_func').textContent = info.device_func || '--';
    document.getElementById('compile_ts').textContent = formatTimestamp(info.compile_ts || 0);

    // 更新UID信息
    document.getElementById('uid_0').textContent = formatHex(info.uid[0] || 0);
    document.getElementById('uid_1').textContent = formatHex(info.uid[1] || 0);
    document.getElementById('uid_2').textContent = formatHex(info.uid[2] || 0);
}
</script>
